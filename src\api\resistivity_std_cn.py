class resistivity_std_cn(Resource):

    def get(self):
        '''标准差判定
        ---
        tags:
          - 电磁
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: database
            in: query
            type: string
            default: 'shandong-official'
            description: 数据库名称
          - name: methodname
            in: query
            type: string
            default: '地电阻率'
            description: 方法名称
          - name: window
            in: query
            type: number
            default: 365
            description: 监视窗长
          - name: scale
            in: query
            type: number
            default: 2.0
            description: 标准差倍数
        responses:
          200:
            description: 用于判定标准差
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('database',
                            type=str,
                            required=False,
                            location='args',
                            help='database')
        parser.add_argument('methodname',
                            type=str,
                            required=False,
                            location='args',
                            help='methodname')
        parser.add_argument('window',
                            type=int,
                            location='args',
                            required=False,
                            help='monitoring window')
        parser.add_argument('scale',
                            type=float,
                            location='args',
                            required=False,
                            help='scale')
        args = parser.parse_args()
        database = args['database']
        methodname = args['methodname']
        monitoring_window = args['window']
        scale = args['scale']
        stations = self.list_stations_by_item(methodname=methodname,
                                              database=database)
        df = self.china_resistivity_std(stations,
                                        database=database,
                                        startdate='20160101',
                                        monitoring_window=monitoring_window,
                                        scale=scale)

        return jsonify(df.to_dict(orient='split'))

    def list_stations_by_item_sqlalchemy(self,
                                         methodname='地电阻率',
                                         database='shandong-official'):
        '''
        Oracle数据库自动映射的表名、列名为小写，
        MySQL数据库自动映射的表名、列名为大写。
        Shit!
        '''
        # connect to database
        os.environ['NLS_LANG'] = 'AMERICAN_AMERICA.ZHS16GBK'
        config = configparser.ConfigParser()
        config.read('default.conf')
        url = sqlalchemy.engine.URL(
            config[database]['DRIVER'], config[database]['USERNAME'],
            parse.quote_plus(config[database]['PASSWORD']),
            config[database]['HOST'], config[database]['PORT'],
            config[database]['TNSNAME'])
        engine = create_engine(url)
        metadata = MetaData()
        metadata.reflect(
            engine,
            schema='qzdata',
            only=['qz_dict_stations', 'qz_dict_stationitems', 'qz_dict_items'])
        Base = automap_base(metadata=metadata)
        Base.prepare()
        qz_dict_stations = Base.classes.qz_dict_stations
        qz_dict_stationitems = Base.classes.qz_dict_stationitems
        qz_dict_items = Base.classes.qz_dict_items
        Session = sessionmaker(bind=engine)
        session = Session()
        sql = session.query(qz_dict_stations.stationid, qz_dict_stations.stationname).\
                        distinct(qz_dict_stations.stationid).\
                            join(qz_dict_stationitems, qz_dict_stations.stationid==qz_dict_stationitems.stationid).\
                                join(qz_dict_items, qz_dict_stationitems.itemid==qz_dict_items.itemid).\
                                    filter(qz_dict_items.itemname.like(f'%{methodname}%')).statement
        stations = pd.read_sql(sql=sql, con=engine)

        return stations

    def list_stations_by_item(self,
                              database='shandong-official',
                              methodname='地电阻率'):

        os.environ['NLS_LANG'] = 'AMERICAN_AMERICA.ZHS16GBK'

        conn = tsf.conn_to_MySQL(database)

        sql = 'SELECT METHODID FROM QZDATA.QZ_DICT_METHODS WHERE METHODNAME LIKE %s'
        df = pd.read_sql(sql, con=conn, params={'%%' + methodname + '%%'})
        methodid = df['METHODID'][0]
        sql = "SELECT DISTINCT A.STATIONID, A.STATIONNAME, B.POINTID FROM " \
            "QZDATA.QZ_DICT_STATIONS A, QZDATA.QZ_DICT_STATIONITEMS B " \
            "WHERE A.STATIONID=B.STATIONID AND SUBSTR(B.ITEMID,1,3)=%s ORDER BY A.STATIONID"
        df = pd.read_sql(sql, con=conn, params={methodid})

        return df

    def china_resistivity_std(self,
                              stations,
                              database='shandong-official',
                              startdate='20160101',
                              monitoring_window=365,
                              scale=2.0):
        '''目前没有考虑点号，对与同台多测点的台站会有问题，可以考虑加一重点号循环'''
        # connect to database
        os.environ['NLS_LANG'] = 'AMERICAN_AMERICA.ZHS16GBK'

        conn = tsf.conn_to_MySQL(database)

        itemid = ['3211', '3212', '3213', '3214']
        ts_df = pd.DataFrame()
        df_std = pd.DataFrame()
        monitoring_startdate = (
            dt.datetime.today() +
            dt.timedelta(-monitoring_window)).strftime('%Y%m%d')
        enddate = dt.datetime.today().strftime('%Y%m%d')
        for index, row in stations.iterrows():
            station = row['stationname']
            pointid = row['pointid']
            print(station)
            try:
                ts_df = tsf.fetching_data(conn,
                                          startdate,
                                          enddate,
                                          '地电阻率观测',
                                          station,
                                          '小时值',
                                          '预处理库',
                                          itemname='地电阻率观测',
                                          gzip_flag=False)
                items = ts_df['ITEMID'].unique()
                itemid = list(set(items) & set(itemid))
                for item in itemid:
                    df = ts_df[ts_df['ITEMID'] == item].resample('1d').median()
                    upper = df['OBSVALUE'].mean(
                    ) + df['OBSVALUE'].std() * scale
                    lower = df['OBSVALUE'].mean(
                    ) - df['OBSVALUE'].std() * scale
                    count = len(df.loc[(df.index >= monitoring_startdate) &
                                       (df.index < enddate) &
                                       ((df['OBSVALUE'] < lower) |
                                        (df['OBSVALUE'] > upper)), 'OBSVALUE'])
                    if count:
                        tmp = pd.DataFrame(
                            [[station, item, count]],
                            columns=['station', 'itemid', 'count_days'])
                        df_std = pd.concat([df_std, tmp])
            except BaseException:
                continue

        return df_std
