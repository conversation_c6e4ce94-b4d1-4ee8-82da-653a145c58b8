name: Build Python Extensions

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-18.04]
        python-version: [3.9]
    runs-on: ${{ matrix.os }}

    steps:
      # 检出代码仓库
      - name: Checkout repository
        uses: actions/checkout@v4

      # 设置 Python 环境
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      # 安装 Cython 和其他依赖
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install cython  # 安装 Cython
          pip install wheel setuptools

      # 编译 .pyx 文件
      - name: Build .pyd file with Cython
        run: |
          cythonize -i src/app.py

      # 上传生成的 .pyd 文件作为构建产物
      - name: Upload .pyd artifact
        uses: actions/upload-artifact@v4
        with:
          name: pyd-file
          path: src/*.so # 将生成的 .pyd 文件上传为构建产物
