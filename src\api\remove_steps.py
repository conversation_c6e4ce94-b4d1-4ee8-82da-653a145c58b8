class remove_steps(Resource):

    def post(self):
        '''傅立叶去年变
        ---
        tags:
          - 数据库
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: upfile
            in: formData
            type: file
            required: true
            description: 利用傅立叶去年变方法提取的去年变后的时间序列（utf-8）

        responses:
          200:
            description: 从文件获取前兆时间序列数据
        '''
        uploaded_file = request.files['upfile']
        data = pd.read_json(uploaded_file, orient='split')
        df = self.remove_steps_by_logs(data)
        return jsonify(df.to_dict(orient='split'))

    def remove_steps_by_logs(self, df):
        df['STATIONID'] = df['STATIONID'].astype(str)
        df['POINTID'] = df['STATIONID'].str.slice(5)
        df['STATIONID'] = df['STATIONID'].str.slice(0, 5)
        print(df)
        step_dir = '../steps'
        step_files = glob.glob('../steps/*.txt')
        items = df['ITEMID'].unique()
        print(items)
        step_files = [os.path.basename(path) for path in step_files]
        for itemid in items:
            print(itemid)
            ts = df[df['ITEMID'] == itemid]['OBSVALUE']
            ts_0 = ts[-1]
            stationid = df.iloc[1, 0]
            pointid = df.iloc[1, 3]
            file = '_'.join([str(stationid), str(pointid), str(itemid)])
            file = '.'.join([file, 'txt'])
            if file in step_files:
                print(file)
                with open(os.path.join(step_dir, file), 'r') as f:
                    steps = f.read().split('\n')
                    if ' ' in steps:
                        steps.remove('')
                    ts.fillna(method='bfill', inplace=True)
                    ts_diff = ts.diff()
                    for step in steps:
                        print(step)
                        ts_diff.loc[step] = 0.0
                    ts = ts_diff.cumsum()
                    ts.fillna(method='bfill', inplace=True)
            ts_1 = ts[-1]
            ts = ts + (ts_0 - ts_1)
            df.loc[df['ITEMID'] == itemid, 'OBSVALUE'] = ts
        return df
