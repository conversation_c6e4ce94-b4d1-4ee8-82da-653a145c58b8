#项目名
[program:geomagenetic_tools]
#脚本目录
directory=/home/<USER>/wangqinglin/geomagnetic_tools
#脚本执行命令
command=python /home/<USER>/wangqinglin/geomagnetic_tools/calling_gt.py
#supervisor启动的时候是否随着同时启动，默认True
autostart=true
#当程序exit的时候，这个program不会自动重启,默认unexpected
#设置子进程挂掉后自动重启的情况，有三个选项，false,unexpected和true。如果为false的时候，无论什么情况下，都不会被重新启动，如果为unexpected，只有当进程的退出码不在下面的exitcodes里面定义的
autorestart=true
#这个选项是子进程启动多少秒之后，此时状态如果是running，则我们认为启动成功了。默认值为1
startsecs=1
#日志输出 
stderr_logfile=/home/<USER>/wangqinglin/geomagnetic_tools/gt_stderr.log
stdout_logfile=home/geoist/wangqinglin/geomagnetic_tools/gt_stdout.log
#脚本运行的用户身份
user = wangqinglin
#把 stderr 重定向到 stdout，默认 false
redirect_stderr = true
#stdout 日志文件大小，默认 50MB
stdout_logfile_maxbytes = 100M
#stdout 日志文件备份数
stdout_logfile_backups = 20
