class fourier_smoothing(Resource):

    def get(self):
        '''傅立叶去年变
        ---
        tags:
          - 数据库
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: database
            in: query
            type: string
            default: 'shandong-official'
            description: 数据库名称
          - name: stationname
            in: query
            type: string
            default: '马陵山'
            description: 台站名称
          - name: startdate
            in: query
            type: string
            default: '20150101'
            description: 起始时间
          - name: enddate
            in: query
            type: string
            default: '20211231'
            description: 结束时间
          - name: methodname
            in: query
            type: string
            default: '地电阻率'
            description: 测向名称，模糊查询
          - name: sampling
            in: query
            type: string
            default: '小时值'
            description: 采样率，模糊查询
          - name: basetype
            in: query
            type: string
            default: '预处理库'
            description: 数据类型：预处理库/原始库/产品库
          - name: itemname
            in: query
            type: string
            default: '直流单装置地电阻率观测东西向'
            description: 测向分量：精确匹配
          - name: pointid
            in: query
            type: string
            default: ''
            description: 测点号
          - name: gaps
            in: query
            type: string
            default: ''
            description: 缺数区间
          - name: returntype
            in: query
            type: boolean
            default: false
            description:  false:JSON数据表/true:JSON图片

        responses:
          200:
            description: 从数据库获取前兆时间序列数据
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('database',
                            type=str,
                            required=False,
                            location='args',
                            help='database')
        parser.add_argument('startdate',
                            type=str,
                            required=False,
                            location='args',
                            help='startdate')
        parser.add_argument('enddate',
                            type=str,
                            required=False,
                            location='args',
                            help='enddate')
        parser.add_argument('stationname',
                            type=str,
                            required=False,
                            location='args',
                            help='stationname')
        parser.add_argument('methodname',
                            type=str,
                            required=False,
                            location='args',
                            help='methodname')
        parser.add_argument('sampling',
                            type=str,
                            required=False,
                            location='args',
                            help='sampling')
        parser.add_argument('basetype',
                            type=str,
                            required=False,
                            location='args',
                            help='basetype')
        parser.add_argument('itemname',
                            type=str,
                            required=False,
                            location='args',
                            help='itemname')
        parser.add_argument('pointid',
                            type=str,
                            required=False,
                            location='args',
                            help='pointid')
        parser.add_argument('gaps',
                            type=str,
                            required=False,
                            location='args',
                            help='gaps')
        args = parser.parse_args()
        database = args['database']
        stationname = args['stationname']
        startdate = args['startdate']
        enddate = args['enddate']
        methodname = args['methodname']
        sampling = args['sampling']
        basetype = args['basetype']
        itemname = args['itemname']
        pointid = args['pointid']

        if not pointid:
            pointid = ''

        conn = tsf.conn_to_MySQL(database)

        df = tsf.fetching_data(conn,
                               startdate,
                               enddate,
                               methodname,
                               stationname,
                               sampling,
                               basetype,
                               gzip_flag=False,
                               itemname=itemname,
                               pointid=pointid)
        df = self.fourier_removing(df)

        return jsonify(df.to_dict(orient='split'))

    def post(self):
        '''傅立叶去年变
        ---
        tags:
          - 数据库
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: upfile
            in: formData
            type: file
            required: true
            description: 利用傅立叶去年变方法提取的去年变后的时间序列（utf-8）

        responses:
          200:
            description: 从文件获取前兆时间序列数据
        '''

        uploaded_file = request.files['upfile']
        data = pd.read_json(uploaded_file)
        df = self.fourier_removing(data)
        return jsonify(df.to_dict(orient='split'))

    @staticmethod
    def fourier(df, T=365):
        n = df.shape[0]
        df.fillna(inplace=True, method='pad', limit=181)
        w = pd.Series(range(0, n))
        tmp = pd.DataFrame()
        tmp['w'] = w
        tmp['o'] = df.values
        a = 2 / T * sum(tmp['o'] * cos(2 * pi * (tmp['w'] - n + T) / T))
        return a

    def fourier_removing(self, df):
        df.drop(['ITEMID', 'STATIONID', 'POINTID'], axis=1, inplace=True)
        df = df.resample('1D').mean(numeric_only=True)
        df['OBSVALUE'].fillna(inplace=True, method='ffill')
        df['fourier'] = df['OBSVALUE'].rolling(365).apply(self.fourier)
        df_1 = df['OBSVALUE'].iloc[:365 * 2]
        df_1_f = df_1[::-1].rolling(365).apply(self.fourier)
        df_1_f = df_1_f[::-1]
        df['fourier'].iloc[:365] = df_1_f.iloc[:365]
        df['fourier'].iloc[365:] = df['fourier'].iloc[365:] - df[
            'fourier'].iloc[365] + df['fourier'].iloc[364]

        df['residual'] = df['OBSVALUE'] - df['fourier']

        return df
