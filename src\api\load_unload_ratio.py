class load_unload_ratio(Resource):

    def get(self):
        '''地磁加卸载响应比
        ---
        tags:
          - 电磁
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: database
            in: query
            type: string
            default: 'BAIJIATUAN'
            description: 数据库名称
          - name: stationname
            in: query
            type: string
            default: '泰安'
            description: 台站名称
          - name: startdate
            in: query
            type: string
            default: '20210101'
            description: 起始时间
          - name: enddate
            in: query
            type: string
            default: '20210131'
            description: 结束时间
          - name: returntype
            in: query
            type: boolean
            default: false
            description:  false:JSON数据表/true:JSON图片

        responses:
          200:
            description: 用于计算地磁加卸载响应比
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('database',
                            type=str,
                            required=False,
                            location='args',
                            help='database')
        parser.add_argument('startdate',
                            type=str,
                            required=False,
                            location='args',
                            help='startdate')
        parser.add_argument('enddate',
                            type=str,
                            required=False,
                            location='args',
                            help='enddate')
        parser.add_argument('stationname',
                            type=str,
                            required=False,
                            location='args',
                            help='stationname')
        parser.add_argument('returntype',
                            type=str,
                            required=False,
                            location='args',
                            help='return type')
        args = parser.parse_args()
        database = args['database']
        stationname = args['stationname']
        startdate = args['startdate']
        enddate = args['enddate']
        returntype = args['returntype']
        if returntype == 'true':
            df = self.magnetic_load_unload_ratio_vertical(
                startdate,
                enddate,
                stationname=stationname,
                figure_or_not=True,
                database=database)
            return jsonify({'figure': df})
        else:
            df = self.magnetic_load_unload_ratio_vertical(
                startdate,
                enddate,
                stationname=stationname,
                figure_or_not=False,
                database=database)
            return jsonify(df.to_dict(orient='dict'))

    def magnetic_load_unload_ratio_vertical(self,
                                            startdate,
                                            enddate,
                                            stationname='全部',
                                            figure_or_not=True,
                                            database='BAIJIATUAN'):

        conn = tsf.conn_to_MySQL(database)
        df = tsf.fetching_data(conn,
                               startdate,
                               enddate,
                               '地磁',
                               stationname,
                               '分钟值',
                               '预处理库',
                               gzip_flag=False,
                               itemname=['变化记录垂直分量Z'])
        stations, points, items = tsf.timeseries_info(conn)
        # Fourier filter
        df = df.tz_localize('UTC')
        df = df.tz_convert('Asia/Shanghai')
        # groupby STATIONID
        df = df.pivot_table(values='OBSVALUE',
                            index='STARTDATE',
                            columns='STATIONID')
        # ratio
        days = pd.date_range(df.index.min(), df.index.max())
        df_amplitude = pd.DataFrame()
        for i1 in days:
            day = i1.strftime('%Y-%m-%d')
            # Fourier filter
            df_amplitude_tmp = amplitude_ratio.harmonic_analysis_for_mdrv(
                df.loc[day], order=48)
            # df_amplitude_tmp = df.loc[day]
            amplitude = df_amplitude_tmp.max() - df_amplitude_tmp.min()
            df_amplitude = df_amplitude.append(amplitude, ignore_index=True)
        df_amplitude.index = days.strftime('%Y-%m-%d')
        df_ratio = self.multi_stations_load_unload(df_amplitude)
        # add station name
        sta = df_ratio.columns.tolist()
        station_name = list()
        for i in range(df_ratio.shape[1]):
            station_name.append(
                stations[stations['STATIONID'] == sta[i][0:5]].iloc[0, 1] +
                sta[i])
        # df_ratio.columns = station_name
        if figure_or_not:
            pd.options.plotting.backend = 'plotly'
            trace = list()
            for column in df.columns.values:
                tmp_trace = go.Scatter(
                    x=df_ratio[column].index,
                    y=df_ratio[column],
                    mode='lines+markers+text',
                    connectgaps=True,
                    text=df_ratio[column].round(2).astype('str'),
                    textposition="bottom center")
                trace.append(tmp_trace)
            fig = make_subplots(rows=len(trace),
                                cols=1,
                                subplot_titles=station_name,
                                x_title="Date",
                                y_title="",
                                shared_xaxes=True)
            for i in range(0, len(trace)):
                fig.append_trace(trace[i], i + 1, 1)
            fig.update_layout(height=200 * len(trace),
                              width=1200,
                              title_text='地磁加卸载响应比')
            df_ratio = fig.to_json()

        return df_ratio

    @staticmethod
    def multi_stations_load_unload(df):
        for column in df.columns.values:
            df_tmp = pd.DataFrame()
            df_tmp['OBSVALUE'] = df[column]
            df_tmp = load_unload_ratio.load_unload(df_tmp)
            df[column] = df_tmp['OBSVALUE']

        return df

    @staticmethod
    def load_unload(df):
        '''
        FLAG=0
        FLAG=+1 local max
        FLAG=-1 local min
        '''
        df['FLAG'] = 0
        amp_max = argrelmax(df['OBSVALUE'].values)
        amp_min = argrelmin(df['OBSVALUE'].values)
        df.iloc[list(amp_max), 1] = +1
        df.iloc[list(amp_min), 1] = -1
        df = df[~df['FLAG'].isin([0])]
        amplitude_max = df[:-1]
        amplitude_min = df[+1:]
        amplitude_max.index = amplitude_min.index
        df_ratio = amplitude_max / amplitude_min
        df_ratio['FLAG'] = amplitude_max['FLAG']
        df_ratio = df_ratio[~df_ratio['FLAG'].isin([-1])]
        df_ratio.drop('FLAG', axis=1, inplace=True)

        return df_ratio
