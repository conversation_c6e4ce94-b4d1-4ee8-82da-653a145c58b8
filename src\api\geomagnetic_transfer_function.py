class geomagnetic_transfer_function(Resource):

    def post(self):
        '''地磁转换函数法
        ---
        tags:
          - 电磁
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: stationname
            in: query
            type: string
            default: '安丘'
            description: 台站名称
          - name: startdate
            in: query
            type: string
            default: '20210101'
            description: 起始时间
          - name: enddate
            in: query
            type: string
            default: '20210131'
            description: 结束时间
          - name: returntype
            in: query
            type: boolean
            default: false
            description:  false:JSON数据表/true:JSON图片

        responses:
          200:
            description: 用于计算地磁转换函数
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('startdate',
                            type=str,
                            required=False,
                            location='args',
                            help='startdate')
        parser.add_argument('enddate',
                            type=str,
                            required=False,
                            location='args',
                            help='enddate')
        parser.add_argument('stationname',
                            type=str,
                            required=False,
                            location='args',
                            help='stationname')
        parser.add_argument('returntype',
                            type=str,
                            required=False,
                            location='args',
                            help='return type')
        args = parser.parse_args()
        stationname = args['stationname']
        startdate = args['startdate']
        enddate = args['enddate']
        returntype = args['returntype']
        if returntype == 'true':
            df = self.geomagnetic_tf(startdate,
                                     enddate,
                                     stationname,
                                     figure_or_not=True)
            return jsonify({'figure': df})
        else:
            df = self.geomagnetic_tf(startdate,
                                     enddate,
                                     stationname,
                                     figure_or_not=False)
            return jsonify(df.to_dict(orient='dict'))

    def geomagnetic_tf(self,
                       startdate,
                       enddate,
                       stationname,
                       figure_or_not=True,
                       database='BAIJIATUAN'):

        conn = tsf.conn_to_MySQL(database)
        df = tsf.fetching_data(conn,
                               startdate,
                               enddate,
                               '地磁',
                               stationname,
                               '分钟值',
                               '预处理库',
                               gzip_flag=False,
                               itemname=['变化记录'])
        for i in pd.date_range(start=startdate, end=enddate, freq='5D'):
            df_step = df[i:(i + dt.timedelta(days=4))]
            tripper_step = self.geomagnetic_tf_by_steps(df_step)

        return tripper_step

    def geomagnetic_tf_by_steps(self, df):
        sampling = 1.0
        start = 0
        nb_freq = 41
        freq = np.logspace(-4, np.log10(0.25), nb_freq)
        hx = df[df['ITEMID'] == '3124']['OBSVALUE'].values
        hy = df[df['ITEMID'] == '3125']['OBSVALUE'].values
        hz = df[df['ITEMID'] == '3123']['OBSVALUE'].values
        ex = df[df['ITEMID'] == '3123']['OBSVALUE'].values
        ey = df[df['ITEMID'] == '3123']['OBSVALUE'].values
        signal = rb.SyncSignal([hx, hy, hz, ex, ey], sampling, start)
        sig = rb.SignalSet(
            {
                'Site001_Hx': 0,
                'Site001_Hy': 1,
                'Site001_Hz': 2,
                'Site001_Ex': 3,
                'Site001_Ey': 4,
                'B': (0, 1, 2),
                'E': (3, 4)
            }, signal)
        inv = rb.Inventory()
        inv.append(sig)
        res = rb.utils.impedance(sig, freq)
        # rho = 1e12 * np.abs(res.impedance) ** 2 / freq[:, None, None]
        # rho_err = 1e12 * np.abs(res.error) ** 2 / freq[:, None, None]
        # phi = np.angle(res.impedance, deg=True)
        # rad_err = np.arcsin(res.error / abs(res.impedance))
        # rad_err[np.isnan(rad_err)] = np.pi
        # phi_err = np.rad2deg(rad_err)

        return res
