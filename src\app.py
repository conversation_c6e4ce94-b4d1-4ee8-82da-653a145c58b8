import os
import sys
import glob
import cx_Oracle
import configparser
from urllib import parse
import datetime as dt
import numpy as np
import matplotlib
import matplotlib.pyplot as plt
import pandas as pd
import plotly.express as px
import plotly.graph_objs as go
from plotly.subplots import make_subplots
import razorback as rb
from scipy import signal
from scipy.signal import argrelmax, argrelmin
from flasgger import Swagger
from flask import Flask, request, jsonify, make_response
from flask_cors import CORS
from flask_restful import reqparse, Api, Resource
from addereq import fetching as tsf
from matplotlib import rcParams
from matplotlib.pyplot import cm
from numpy import pi, sin, cos, sqrt, arctan
from numpy.fft import fft
from pandas.plotting import register_matplotlib_converters
import sqlalchemy
from sqlalchemy.ext.automap import automap_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import MetaData, create_engine

register_matplotlib_converters()
if sys.platform == 'darwin':
    cx_Oracle.init_oracle_client(
        lib_dir='/Users/<USER>/Work.localized/Python/instantclient')

app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False
CORS(app, supports_credentials=True)
api = Api(app)

UPLOAD_PATH = os.path.abspath('.')

swagger_config = Swagger.DEFAULT_CONFIG
swagger_config['title'] = '电磁学科时间序列处理接口'
swagger_config['description'] = '电磁学科时间序列处理方法，差分、地磁加卸载响应比、地电场优势方位角等'
swagger_config['host'] = '0.0.0.0:8888'
swagger_config['optional_fields'] = ['components']
swagger_config['version'] = ['1.0.0']

swagger = Swagger(app, config=swagger_config)
# swagger = Swagger(app)


@app.route('/', methods=['POST', 'GET'])
def hello():
    '''电磁学科时间序列数据处理API
    
    tags:
      - Welcome to using online calculator API.
    responses:
      200:
        description:  电磁学科时间序列数据处理API
    '''

    return '欢迎使用电磁学科时间序列数据处理API！'


def conn_to_Oracle(db):
    """Connect to Oracle database."""
    config = configparser.ConfigParser()
    home_dir = os.path.expanduser("~")
    file_path = os.path.join(home_dir, ".adder", "default.conf")
    config.read(['default.conf', file_path])

    if not config.sections() or db not in config:
        print("数据库配置文件不存在或缺少配置项，请检查并配置 default.conf")
        return None

    try:
        host = config.get(db, 'HOST')
        port = config.get(db, 'PORT')
        username = config.get(db, 'USERNAME')
        password = config.get(db, 'PASSWORD')
        tns = config.get(db, 'TNSNAME')

        db_url = f'oracle+cx_oracle://{username}:{password}@{host}:{port}/{tns}'
        engine = create_engine(db_url, echo=False)
        print(config.get(db, 'HOST') + ' connected...')
        return engine
    except Exception as e:
        print("连接数据库时发生错误，", str(e))
        return None


class amplitude_ratio(Resource):

    def get(self):
        '''地磁逐日比
        ---
        tags:
          - 电磁
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: database
            in: query
            type: string
            default: 'shandong-official'
            description: 数据库名称
          - name: stationname
            in: query
            type: string
            default: '泰安'
            description: 台站名称
          - name: startdate
            in: query
            type: string
            default: '20210401'
            description: 起始时间
          - name: enddate
            in: query
            type: string
            default: '20210410'
            description: 结束时间
          - name: returntype
            in: query
            type: boolean
            default: false
            description: false:JSON数据表/true:JSON图片

        responses:
          200:
            description: 用于计算地磁逐日比
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('database',
                            type=str,
                            required=False,
                            location='args',
                            help='database')
        parser.add_argument('startdate',
                            type=str,
                            required=False,
                            location='args',
                            help='startdate')
        parser.add_argument('enddate',
                            type=str,
                            required=False,
                            location='args',
                            help='enddate')
        parser.add_argument('stationname',
                            type=str,
                            required=False,
                            location='args',
                            help='stationname')
        parser.add_argument('returntype',
                            type=str,
                            required=False,
                            location='args',
                            help='return type')
        args = parser.parse_args()
        print(args)
        database = args['database']
        startdate = args['startdate']
        enddate = args['enddate']
        stationname = args['stationname']
        returntype = args['returntype']
        print(returntype)
        if returntype == 'true':
            df = self.magnetic_daily_ratio_vertical(startdate,
                                                    enddate,
                                                    stationname=stationname,
                                                    figure_or_not=True,
                                                    database=database)
            return jsonify({'figure': df})
        else:
            df = self.magnetic_daily_ratio_vertical(startdate,
                                                    enddate,
                                                    stationname=stationname,
                                                    figure_or_not=False,
                                                    database=database)
            return jsonify(df.to_dict(orient='dict'))

    @staticmethod
    def harmonic_analysis(df, order):
        df.fillna(method='bfill', inplace=True)
        n = df.shape[0]
        df['IND'] = range(1, n + 1)
        df['FITTING'] = df['OBSVALUE'].mean()
        for i in range(1, order + 1):
            ak = sum(
                df['OBSVALUE'] * cos(2.0 * pi * df['IND'] / n * i)) / n * 2.0
            bk = sum(
                df['OBSVALUE'] * sin(2.0 * pi * df['IND'] / n * i)) / n * 2.0
            df['FITTING'] = df['FITTING'] \
                            + ak * cos(2.0 * pi * df['IND'] * i / n) \
                            + bk * sin(2.0 * pi * df['IND'] * i / n)
        return df

    @staticmethod
    def lowpass_filter(df):
        df.fillna(method='bfill', inplace=True)
        b, a = signal.butter(N=8,
                             Wn=1 / 2400,
                             btype='lowpass',
                             fs=1 / 60,
                             analog=False,
                             output='ba')
        f_pad = signal.filtfilt(b=b, a=a, x=df['OBSVALUE'].values, padlen=50)
        df['FITTING'] = f_pad
        return df

    @staticmethod
    def harmonic_analysis_for_mdrv(df, order=48):
        for column in df.columns.values:
            df_ha = pd.DataFrame()
            df_ha['OBSVALUE'] = df[column]
            df_ha = amplitude_ratio.lowpass_filter(df_ha)
            df[column] = df_ha['FITTING']
        return df

    def magnetic_daily_ratio_vertical(
        self,
        startdate,
        enddate,
        stationname='全部',
        figure_or_not=True,
        database='BAIJIATUAN',
    ):

        conn = tsf.conn_to_MySQL(database)
        df = tsf.fetching_data(conn,
                               startdate,
                               enddate,
                               '地磁',
                               stationname,
                               '分钟值',
                               '预处理库',
                               gzip_flag=False,
                               itemname='变化记录垂直分量Z')
        stations, points, items = tsf.timeseries_info(conn)
        df = df.tz_localize('UTC')
        df = df.tz_convert('Asia/Shanghai')
        # groupby STATIONID
        df = df.pivot_table(values='OBSVALUE',
                            index='STARTDATE',
                            columns='STATIONID')
        # ratio
        days = pd.date_range(df.index.min(), df.index.max())
        df_amplitude = pd.DataFrame()
        for i1 in days:
            day = i1.strftime('%Y-%m-%d')
            # Fourier filter
            df_amplitude_tmp = self.harmonic_analysis_for_mdrv(df.loc[day],
                                                               order=48)
            amplitude = df_amplitude_tmp.max() - df_amplitude_tmp.min()
            df_amplitude = df_amplitude.append(amplitude, ignore_index=True)
        df_amplitude.index = days.strftime('%Y-%m-%d')

        amplitude_1 = df_amplitude[+1:]
        amplitude_2 = df_amplitude[:-1]
        amplitude_2.index = amplitude_1.index
        df_ratio = amplitude_2 / amplitude_1

        # add station name
        sta = df_ratio.columns.tolist()
        if figure_or_not:
            station_name = list()
            for i in range(df_ratio.shape[1]):
                station_name.append(
                    stations[stations['STATIONID'] == sta[i][0:5]].iloc[0, 1] +
                    sta[i])
            # df_ratio.columns = station_name
            pd.options.plotting.backend = 'plotly'
            trace = list()
            for column in df.columns.values:
                trace.append(go.Scatter(x=df_ratio.index, y=df_ratio[column]))
            fig = make_subplots(
                rows=len(trace),
                cols=1,
                subplot_titles=station_name,
                x_title="Date",
                y_title="",
            )
            for i in range(0, len(trace)):
                fig.append_trace(trace[i], i + 1, 1)
            fig.update_layout(height=200 * len(trace),
                              width=1200,
                              title_text='地磁逐日比')
            df_ratio = fig.to_json()

        return df_ratio


class load_unload_ratio(Resource):

    def get(self):
        '''地磁加卸载响应比
        ---
        tags:
          - 电磁
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: database
            in: query
            type: string
            default: 'BAIJIATUAN'
            description: 数据库名称
          - name: stationname
            in: query
            type: string
            default: '泰安'
            description: 台站名称
          - name: startdate
            in: query
            type: string
            default: '20210101'
            description: 起始时间
          - name: enddate
            in: query
            type: string
            default: '20210131'
            description: 结束时间
          - name: returntype
            in: query
            type: boolean
            default: false
            description:  false:JSON数据表/true:JSON图片

        responses:
          200:
            description: 用于计算地磁加卸载响应比
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('database',
                            type=str,
                            required=False,
                            location='args',
                            help='database')
        parser.add_argument('startdate',
                            type=str,
                            required=False,
                            location='args',
                            help='startdate')
        parser.add_argument('enddate',
                            type=str,
                            required=False,
                            location='args',
                            help='enddate')
        parser.add_argument('stationname',
                            type=str,
                            required=False,
                            location='args',
                            help='stationname')
        parser.add_argument('returntype',
                            type=str,
                            required=False,
                            location='args',
                            help='return type')
        args = parser.parse_args()
        database = args['database']
        stationname = args['stationname']
        startdate = args['startdate']
        enddate = args['enddate']
        returntype = args['returntype']
        if returntype == 'true':
            df = self.magnetic_load_unload_ratio_vertical(
                startdate,
                enddate,
                stationname=stationname,
                figure_or_not=True,
                database=database)
            return jsonify({'figure': df})
        else:
            df = self.magnetic_load_unload_ratio_vertical(
                startdate,
                enddate,
                stationname=stationname,
                figure_or_not=False,
                database=database)
            return jsonify(df.to_dict(orient='dict'))

    def magnetic_load_unload_ratio_vertical(self,
                                            startdate,
                                            enddate,
                                            stationname='全部',
                                            figure_or_not=True,
                                            database='BAIJIATUAN'):

        conn = tsf.conn_to_MySQL(database)
        df = tsf.fetching_data(conn,
                               startdate,
                               enddate,
                               '地磁',
                               stationname,
                               '分钟值',
                               '预处理库',
                               gzip_flag=False,
                               itemname=['变化记录垂直分量Z'])
        stations, points, items = tsf.timeseries_info(conn)
        # Fourier filter
        df = df.tz_localize('UTC')
        df = df.tz_convert('Asia/Shanghai')
        # groupby STATIONID
        df = df.pivot_table(values='OBSVALUE',
                            index='STARTDATE',
                            columns='STATIONID')
        # ratio
        days = pd.date_range(df.index.min(), df.index.max())
        df_amplitude = pd.DataFrame()
        for i1 in days:
            day = i1.strftime('%Y-%m-%d')
            # Fourier filter
            df_amplitude_tmp = amplitude_ratio.harmonic_analysis_for_mdrv(
                df.loc[day], order=48)
            # df_amplitude_tmp = df.loc[day]
            amplitude = df_amplitude_tmp.max() - df_amplitude_tmp.min()
            df_amplitude = df_amplitude.append(amplitude, ignore_index=True)
        df_amplitude.index = days.strftime('%Y-%m-%d')
        df_ratio = self.multi_stations_load_unload(df_amplitude)
        # add station name
        sta = df_ratio.columns.tolist()
        station_name = list()
        for i in range(df_ratio.shape[1]):
            station_name.append(
                stations[stations['STATIONID'] == sta[i][0:5]].iloc[0, 1] +
                sta[i])
        # df_ratio.columns = station_name
        if figure_or_not:
            pd.options.plotting.backend = 'plotly'
            trace = list()
            for column in df.columns.values:
                tmp_trace = go.Scatter(
                    x=df_ratio[column].index,
                    y=df_ratio[column],
                    mode='lines+markers+text',
                    connectgaps=True,
                    text=df_ratio[column].round(2).astype('str'),
                    textposition="bottom center")
                trace.append(tmp_trace)
            fig = make_subplots(rows=len(trace),
                                cols=1,
                                subplot_titles=station_name,
                                x_title="Date",
                                y_title="",
                                shared_xaxes=True)
            for i in range(0, len(trace)):
                fig.append_trace(trace[i], i + 1, 1)
            fig.update_layout(height=200 * len(trace),
                              width=1200,
                              title_text='地磁加卸载响应比')
            df_ratio = fig.to_json()

        return df_ratio

    @staticmethod
    def multi_stations_load_unload(df):
        for column in df.columns.values:
            df_tmp = pd.DataFrame()
            df_tmp['OBSVALUE'] = df[column]
            df_tmp = load_unload_ratio.load_unload(df_tmp)
            df[column] = df_tmp['OBSVALUE']

        return df

    @staticmethod
    def load_unload(df):
        '''
        FLAG=0
        FLAG=+1 local max
        FLAG=-1 local min
        '''
        df['FLAG'] = 0
        amp_max = argrelmax(df['OBSVALUE'].values)
        amp_min = argrelmin(df['OBSVALUE'].values)
        df.iloc[list(amp_max), 1] = +1
        df.iloc[list(amp_min), 1] = -1
        df = df[~df['FLAG'].isin([0])]
        amplitude_max = df[:-1]
        amplitude_min = df[+1:]
        amplitude_max.index = amplitude_min.index
        df_ratio = amplitude_max / amplitude_min
        df_ratio['FLAG'] = amplitude_max['FLAG']
        df_ratio = df_ratio[~df_ratio['FLAG'].isin([-1])]
        df_ratio.drop('FLAG', axis=1, inplace=True)

        return df_ratio


class geomagnetic_harmonic_ratio(Resource):

    def get(self):
        '''地磁谐波振幅比
        ---
        tags:
          - 电磁
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: stationname
            in: query
            type: string
            default: '泰安'
            description: 台站名称
          - name: database
            in: query
            type: string
            default: 'shandong-official'
            description: 数据库名称
          - name: startdate
            in: query
            type: string
            default: '20210101'
            description: 起始时间
          - name: enddate
            in: query
            type: string
            default: '20210131'
            description: 结束时间
          - name: returntype
            in: query
            type: boolean
            default: false
            description:  false:JSON数据表/true:JSON图片

        responses:
          200:
            description: 用于计算地磁谐波振幅比
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('startdate',
                            type=str,
                            required=False,
                            location='args',
                            help='startdate')
        parser.add_argument('enddate',
                            type=str,
                            required=False,
                            location='args',
                            help='enddate')
        parser.add_argument('stationname',
                            type=str,
                            required=False,
                            location='args',
                            help='stationname')
        parser.add_argument('database',
                            type=str,
                            required=False,
                            location='args',
                            help='database')
        parser.add_argument('returntype',
                            type=str,
                            required=False,
                            location='args',
                            help='return type')
        args = parser.parse_args()
        stationname = args['stationname']
        database = args['database']
        startdate = args['startdate']
        enddate = args['enddate']
        returntype = args['returntype']
        if returntype == 'true':
            df = self.harmonic_ratio(startdate,
                                     enddate,
                                     stationname=stationname,
                                     figure_or_not=True,
                                     database=database)
            return jsonify({'figure': df})
        else:
            df = self.harmonic_ratio(startdate,
                                     enddate,
                                     stationname=stationname,
                                     figure_or_not=False,
                                     database=database)
            return jsonify(df.to_dict(orient='dict'))

    def harmonic_ratio(self,
                       startdate,
                       enddate,
                       stationname='泰安',
                       figure_or_not=True,
                       database='BAIJIATUAN'):

        conn = tsf.conn_to_MySQL(database)
        df = tsf.fetching_data(conn,
                               startdate,
                               enddate,
                               '地磁',
                               stationname,
                               '分钟值',
                               '预处理库',
                               gzip_flag=False)
        stations, points, items = tsf.timeseries_info(conn)
        # TimeZone
        df = df.tz_localize('UTC')
        df = df.tz_convert('Asia/Shanghai')
        df_harmonic = self.calculate_harmonic_ratio(df)
        if figure_or_not:
            trace = px.scatter(df_harmonic.rolling(30).mean(),
                               labels={
                                   'index': 'Date',
                                   'value': 'Harmonic ratio',
                                   'variable': '测向'
                               },
                               title='地磁谐波振幅比')
            trace.show()
            df_harmonic = trace.to_json()
        return df_harmonic

    def calculate_harmonic_ratio(self, df):
        df_z = df[(df['ITEMID'] == '3123') & (df['STATIONID'] == '370011')]
        df_h = df[(df['ITEMID'] == '3124') & (df['STATIONID'] == '370011')]
        days = pd.date_range(df.index.min(), df.index.max(), freq='1D')
        df_harmonic = pd.DataFrame()
        for i in days:
            day = i.strftime('%Y-%m-%d')
            print(day)
            # df_oneday_z = df_z.loc[day: (i + dt.timedelta(days=5)).strftime('%Y-%m-%d')]
            # df_oneday_h = df_h.loc[day: (i + dt.timedelta(days=5)).strftime('%Y-%m-%d')]
            df_oneday_z = df_z.loc[day]
            df_oneday_h = df_h.loc[day]
            df_harmonic_oneday = self.calculate_harmonic_oneday(
                df_oneday_z, df_oneday_h)
            df_harmonic = df_harmonic.append(df_harmonic_oneday,
                                             ignore_index=True)
        df_harmonic.index = days.strftime('%Y-%m-%d')
        return df_harmonic

    def calculate_harmonic_oneday(self, df_1, df_2):
        import pydsm
        df_harmonic_oneday = dict()
        df_1.fillna(method='bfill', inplace=True, limit=1440)
        df_2.fillna(method='bfill', inplace=True, limit=1440)
        try:
            df_1['detrend'] = signal.detrend(df_1['OBSVALUE'])
            df_2['detrend'] = signal.detrend(df_2['OBSVALUE'])
            F_1 = pydsm.ft.dtft(df_1['detrend'], fs=1 / 60)
            F_2 = pydsm.ft.dtft(df_2['detrend'], fs=1 / 60)
            freq = 1 / 600
            # freq = 5.78125e-05
            df_harmonic_oneday['harmonic'] = abs(F_1(freq)) / abs(F_2(freq))
        except:
            df_harmonic_oneday['harmonic'] = np.nan

        return df_harmonic_oneday

    @staticmethod
    def fft_ck(df, order):
        df.fillna(method='bfill', inplace=True)
        df_fft = fft(df['OBSVALUE'])
        tmp = 2.0 * abs(df_fft) / 1440
        ck = sum(tmp[1:order + 1])
        return ck


class eleAzimuth(Resource):

    def get(self):
        '''地电场优势方位角
        ---
        tags:
          - 电磁
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: stationname
            in: query
            type: string
            default: '安丘'
            description: 台站名称
          - name: itemname
            in: query
            type: string
            default: '第一'
            description: 第一装置/第二装置
          - name: database
            in: query
            type: string
            default: 'shandong-official'
            description: 数据库名称
          - name: startdate
            in: query
            type: string
            default: '20210101'
            description: 起始时间
          - name: enddate
            in: query
            type: string
            default: '20210131'
            description: 结束时间
          - name: returntype
            in: query
            type: boolean
            default: false
            description:  false:JSON数据表/true:JSON图片

        responses:
          200:
            description: 用于计算地电场优势方位角
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('startdate',
                            type=str,
                            required=False,
                            location='args',
                            help='startdate')
        parser.add_argument('enddate',
                            type=str,
                            required=False,
                            location='args',
                            help='enddate')
        parser.add_argument('stationname',
                            type=str,
                            required=False,
                            location='args',
                            help='stationname')
        parser.add_argument('itemname',
                            type=str,
                            required=False,
                            location='args',
                            help='itemname')
        parser.add_argument('database',
                            type=str,
                            required=False,
                            location='args',
                            help='database')
        parser.add_argument('returntype',
                            type=str,
                            required=False,
                            location='args',
                            help='return type')
        args = parser.parse_args()
        stationname = args['stationname']
        itemname = args['itemname']
        database = args['database']
        startdate = args['startdate']
        enddate = args['enddate']
        returntype = args['returntype']
        if returntype == 'true':
            df = self.geo_ele_Azimuth(startdate,
                                      enddate,
                                      stationname,
                                      itemname,
                                      figure_or_not=True,
                                      database=database)
            return jsonify({'figure': df})
        else:
            df = self.geo_ele_Azimuth(startdate,
                                      enddate,
                                      stationname,
                                      itemname,
                                      figure_or_not=False,
                                      database=database)
            return jsonify(df.to_dict(orient='dict'))

    def geo_ele_Azimuth(self,
                        startdate,
                        enddate,
                        stationname,
                        itemname,
                        figure_or_not=True,
                        database='shandong-official'):

        conn = tsf.conn_to_MySQL(database)
        df = tsf.fetching_data(conn,
                               startdate,
                               enddate,
                               '地电场',
                               stationname,
                               '分钟值',
                               '预处理库',
                               gzip_flag=False,
                               itemname=itemname)
        df_Azimuth = self.calculate_Azimuth(df)
        if figure_or_not:
            trace = px.scatter(df_Azimuth,
                               labels={
                                   'index': 'Date',
                                   'value': 'Azimuth',
                                   'variable': '测向'
                               },
                               title='地电场优势方位角')
            trace.show()
            df_Azimuth = trace.to_json()
        return df_Azimuth

    def calculate_Azimuth(self, df):
        days = pd.date_range(df.index.min(), df.index.max())
        df_Azimuth = pd.DataFrame()
        for i in days:
            day = i.strftime('%Y-%m-%d')
            df_oneday = df.loc[day]
            df_Azimuth_oneday = eleAzimuth.calculate_Azimuth_oneday(df_oneday)
            df_Azimuth = df_Azimuth.append(df_Azimuth_oneday,
                                           ignore_index=True)
        df_Azimuth.index = days.strftime('%Y-%m-%d')
        return df_Azimuth

    @staticmethod
    def calculate_Azimuth_oneday(df):
        amplitude = dict()
        azimuth = dict()
        items = df['ITEMID'].drop_duplicates().sort_values().values
        for item in items:
            amplitude[item] = eleAzimuth.fft_ck(df[df['ITEMID'] == item], 10)
        if '3411' in items:
            try:
                azimuth['EW/NW'] = 90 + 180 / pi * arctan(
                    sqrt(2) * amplitude['3414'] / amplitude['3412'] - 1)
            except:
                pass
            try:
                azimuth['EW/NE'] = 90 - 180 / pi * arctan(
                    sqrt(2) * amplitude['3413'] / amplitude['3412'] - 1)
            except:
                pass
            try:
                azimuth['NS/NW'] = 180 - 180 / pi * arctan(
                    sqrt(2) * amplitude['3414'] / amplitude['3411'] - 1)
            except:
                pass
            try:
                azimuth['NS/NE'] = 180 / pi * arctan(
                    sqrt(2) * amplitude['3413'] / amplitude['3411'] - 1)
            except:
                pass
            try:
                azimuth['NS/EW'] = 180 / pi * arctan(
                    amplitude['3412'] / amplitude['3411'])
            except:
                pass
        elif '3421' in items:
            try:
                azimuth['EW/NW'] = 90 + 180 / pi * arctan(
                    sqrt(2) * amplitude['3424'] / amplitude['3422'] - 1)
            except:
                pass
            try:
                azimuth['EW/NE'] = 90 - 180 / pi * arctan(
                    sqrt(2) * amplitude['3423'] / amplitude['3422'] - 1)
            except:
                pass
            try:
                azimuth['NS/NW'] = 180 - 180 / pi * arctan(
                    sqrt(2) * amplitude['3424'] / amplitude['3421'] - 1)
            except:
                pass
            try:
                azimuth['NS/NE'] = 180 / pi * arctan(
                    sqrt(2) * amplitude['3423'] / amplitude['3421'] - 1)
            except:
                pass
            try:
                azimuth['NS/EW'] = 180 / pi * arctan(
                    amplitude['3422'] / amplitude['3421'])
            except:
                pass
        return azimuth

    @staticmethod
    def harmonic_analysis_ck(df, order):
        df.fillna(method='bfill', inplace=True)
        n = df.shape[0]
        df['IND'] = range(1, n + 1)
        ck = 0
        for i in range(1, order + 1):
            ak_bk = 2.0 * pi * df['IND'] / n * i
            ak = sum(df['OBSVALUE'] * cos(ak_bk)) / n * 2.0
            bk = sum(df['OBSVALUE'] * sin(ak_bk)) / n * 2.0
            ck = sqrt(ak * ak + bk * bk) + ck
        return ck

    @staticmethod
    def fft_ck(df, order):
        df.fillna(method='bfill', inplace=True)
        df_fft = fft(df['OBSVALUE'])
        tmp = 2.0 * abs(df_fft) / 1440
        ck = sum(tmp[1:order + 1])
        return ck


class gantt_plot(Resource):

    def post(self):
        '''时间进程图
        ---
        tags:
          - 画图
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: upfile
            in: formData
            type: file
            required: true
            description: 数据文本文件（utf-8）

        responses:
          200:
            description: 主要用于绘制时间进程图
        '''

        uploaded_file = request.files['upfile']
        parser = reqparse.RequestParser(bundle_errors=True)

        args = parser.parse_args()
        data = pd.read_csv(uploaded_file,
                           sep=' ',
                           parse_dates=True,
                           error_bad_lines=False)
        x1 = pd.to_datetime(data.start)
        x2 = pd.to_datetime(data.end)
        y = data.ind
        names = data.names
        df = self.gantt(x1, x2, y, names)
        return jsonify({'figure': df})

    def gantt_base64(self, x1, x2, y, names):
        from io import BytesIO
        import base64
        rcParams['font.sans-serif'] = ['STSong']
        rcParams['font.style'] = 'normal'

        labs = []
        tickloc = []
        col = []
        color = iter(cm.Dark2(np.linspace(0, 1, len(y))))

        # generate a line and line properties for each station
        plt.subplots(1, 1, figsize=(8, 6))
        ax = plt.subplot(1, 1, 1)
        for i in range(len(y)):
            c = next(color)
            plt.hlines(i + 1, x1[i], x2[i], label=y[i], color=c, linewidth=8)
            # labs.append(names[i].title())
            labs.append(names[i])
            tickloc.append(i + 1)
            col.append(c)
        plt.ylim(0, len(y) + 1)
        plt.yticks(tickloc, labs)

        # create custom x labels
        ax.xaxis.set_major_locator(
            matplotlib.dates.YearLocator(base=5, month=1, day=1))
        ax.xaxis.set_major_formatter(matplotlib.dates.DateFormatter('%Y'))
        # ax.xaxis.set_minor_locator(matplotlib.dates.MonthLocator(interval=6))
        ax.xaxis.set_minor_locator(
            matplotlib.dates.YearLocator(base=1, month=1, day=1))
        plt.xlim(dt.datetime(np.min(x1).year, 1, 1),
                 np.max(x2) + dt.timedelta(days=180))
        plt.xlabel('时间/年')
        plt.ylabel('地震异常')
        # plt.title('Timeline')
        plt.grid(axis='y', linestyle='dotted')

        # color y labels to match lines
        gytl = plt.gca().get_yticklabels()
        for i in range(len(gytl)):
            gytl[i].set_color('black')
        plt.tight_layout()
        cached_IObytes = BytesIO()
        plt.savefig(cached_IObytes, format='png', dpi=300)
        plt.close()
        cached_IObytes.seek(0)
        base64_Data = base64.b64encode(cached_IObytes.read())
        return base64_Data

    def gantt(self, x1, x2, y, names):
        rcParams['font.sans-serif'] = ['STSong']
        rcParams['font.style'] = 'normal'

        labs = []
        tickloc = []
        col = []
        color = iter(cm.Dark2(np.linspace(0, 1, len(y))))

        # generate a line and line properties for each station
        plt.subplots(1, 1, figsize=(8, 6))
        ax = plt.subplot(1, 1, 1)
        for i in range(len(y)):
            c = next(color)
            plt.hlines(i + 1, x1[i], x2[i], label=y[i], color=c, linewidth=8)
            labs.append(names[i].title())
            tickloc.append(i + 1)
            col.append(c)
        plt.ylim(0, len(y) + 1)
        plt.yticks(tickloc, labs)

        # create custom x labels
        ax.xaxis.set_major_locator(
            matplotlib.dates.YearLocator(base=1, month=1, day=1))
        ax.xaxis.set_major_formatter(matplotlib.dates.DateFormatter('%Y'))
        # ax.xaxis.set_minor_locator(matplotlib.dates.MonthLocator(interval=6))
        ax.xaxis.set_minor_locator(
            matplotlib.dates.YearLocator(base=1, month=1, day=1))
        plt.xlim(dt.datetime(np.min(x1).year, 1, 1),
                 np.max(x2) + dt.timedelta(days=180))
        plt.xlabel('时间/年')
        plt.ylabel('地震异常')
        # plt.title('Timeline')
        plt.grid(axis='y', linestyle='dotted')

        # color y labels to match lines
        gytl = plt.gca().get_yticklabels()
        for i in range(len(gytl)):
            gytl[i].set_color('black')
        plt.tight_layout()
        plt.savefig('./figures/gantt.png', format='png', dpi=300)
        plt.close()

        return 'http://192.168.194.131:8888/display/img/gantt.png'

    @app.route('/display/img/<string:filename>', methods=['GET', 'POST'])
    def display_img(filename):
        IMG_PATH = r'/home/<USER>/Documents/Wend/geomagnetic_tools/figures'
        if request.method == 'GET':
            if filename is None:
                pass
            else:
                image_data = open(os.path.join(IMG_PATH, filename),
                                  'rb').read()
                response = make_response(image_data)
                response.headers['Content-Type'] = 'image/png'
                return response
        else:
            pass


class geomagnetic_transfer_function(Resource):

    def post(self):
        '''地磁转换函数法
        ---
        tags:
          - 电磁
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: stationname
            in: query
            type: string
            default: '安丘'
            description: 台站名称
          - name: startdate
            in: query
            type: string
            default: '20210101'
            description: 起始时间
          - name: enddate
            in: query
            type: string
            default: '20210131'
            description: 结束时间
          - name: returntype
            in: query
            type: boolean
            default: false
            description:  false:JSON数据表/true:JSON图片

        responses:
          200:
            description: 用于计算地磁转换函数
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('startdate',
                            type=str,
                            required=False,
                            location='args',
                            help='startdate')
        parser.add_argument('enddate',
                            type=str,
                            required=False,
                            location='args',
                            help='enddate')
        parser.add_argument('stationname',
                            type=str,
                            required=False,
                            location='args',
                            help='stationname')
        parser.add_argument('returntype',
                            type=str,
                            required=False,
                            location='args',
                            help='return type')
        args = parser.parse_args()
        stationname = args['stationname']
        startdate = args['startdate']
        enddate = args['enddate']
        returntype = args['returntype']
        if returntype == 'true':
            df = self.geomagnetic_tf(startdate,
                                     enddate,
                                     stationname,
                                     figure_or_not=True)
            return jsonify({'figure': df})
        else:
            df = self.geomagnetic_tf(startdate,
                                     enddate,
                                     stationname,
                                     figure_or_not=False)
            return jsonify(df.to_dict(orient='dict'))

    def geomagnetic_tf(self,
                       startdate,
                       enddate,
                       stationname,
                       figure_or_not=True,
                       database='BAIJIATUAN'):

        conn = tsf.conn_to_MySQL(database)
        df = tsf.fetching_data(conn,
                               startdate,
                               enddate,
                               '地磁',
                               stationname,
                               '分钟值',
                               '预处理库',
                               gzip_flag=False,
                               itemname=['变化记录'])
        for i in pd.date_range(start=startdate, end=enddate, freq='5D'):
            df_step = df[i:(i + dt.timedelta(days=4))]
            tripper_step = self.geomagnetic_tf_by_steps(df_step)

        return tripper_step

    def geomagnetic_tf_by_steps(self, df):
        sampling = 1.0
        start = 0
        nb_freq = 41
        freq = np.logspace(-4, np.log10(0.25), nb_freq)
        hx = df[df['ITEMID'] == '3124']['OBSVALUE'].values
        hy = df[df['ITEMID'] == '3125']['OBSVALUE'].values
        hz = df[df['ITEMID'] == '3123']['OBSVALUE'].values
        ex = df[df['ITEMID'] == '3123']['OBSVALUE'].values
        ey = df[df['ITEMID'] == '3123']['OBSVALUE'].values
        signal = rb.SyncSignal([hx, hy, hz, ex, ey], sampling, start)
        sig = rb.SignalSet(
            {
                'Site001_Hx': 0,
                'Site001_Hy': 1,
                'Site001_Hz': 2,
                'Site001_Ex': 3,
                'Site001_Ey': 4,
                'B': (0, 1, 2),
                'E': (3, 4)
            }, signal)
        inv = rb.Inventory()
        inv.append(sig)
        res = rb.utils.impedance(sig, freq)
        # rho = 1e12 * np.abs(res.impedance) ** 2 / freq[:, None, None]
        # rho_err = 1e12 * np.abs(res.error) ** 2 / freq[:, None, None]
        # phi = np.angle(res.impedance, deg=True)
        # rad_err = np.arcsin(res.error / abs(res.impedance))
        # rad_err[np.isnan(rad_err)] = np.pi
        # phi_err = np.rad2deg(rad_err)

        return res


class fetching_data_API(Resource):

    def get(self):
        '''从数据库获取前兆时间序列数据
        ---
        tags:
          - 数据库
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: database
            in: query
            type: string
            default: 'BAIJIATUAN'
            description: 数据库名称
          - name: stationname
            in: query
            type: string
            default: '安丘'
            description: 台站名称
          - name: startdate
            in: query
            type: string
            default: '20210101'
            description: 起始时间
          - name: enddate
            in: query
            type: string
            default: '20210131'
            description: 结束时间
          - name: methodname
            in: query
            type: string
            default: '地磁'
            description: 测向名称，模糊查询
          - name: sampling
            in: query
            type: string
            default: '分钟值'
            description: 采样率，模糊查询
          - name: basetype
            in: query
            type: string
            default: '预处理库'
            description: 数据类型：预处理库/原始库/产品库
          - name: itemname
            in: query
            type: string
            default: '全部'
            description: 测向分量：精确匹配
          - name: pointid
            in: query
            type: string
            default: ''
            description: 测点号

        responses:
          200:
            description: 从数据库获取前兆时间序列数据
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('database',
                            type=str,
                            required=False,
                            location='args',
                            help='database')
        parser.add_argument('startdate',
                            type=str,
                            required=False,
                            location='args',
                            help='startdate')
        parser.add_argument('enddate',
                            type=str,
                            required=False,
                            location='args',
                            help='enddate')
        parser.add_argument('stationname',
                            type=str,
                            required=False,
                            location='args',
                            help='stationname')
        parser.add_argument('methodname',
                            type=str,
                            required=False,
                            location='args',
                            help='methodname')
        parser.add_argument('sampling',
                            type=str,
                            required=False,
                            location='args',
                            help='sampling')
        parser.add_argument('basetype',
                            type=str,
                            required=False,
                            location='args',
                            help='basetype')
        parser.add_argument('itemname',
                            type=str,
                            required=False,
                            location='args',
                            help='itemname')
        parser.add_argument('pointid',
                            type=str,
                            required=False,
                            location='args',
                            help='pointid')
        args = parser.parse_args()
        database = args['database']
        stationname = args['stationname']
        startdate = args['startdate']
        enddate = args['enddate']
        methodname = args['methodname']
        sampling = args['sampling']
        basetype = args['basetype']
        itemname = args['itemname']
        pointid = args['pointid']
        if not pointid:
            pointid = ''

        conn = tsf.conn_to_MySQL(database)
        df = tsf.fetching_data(conn,
                               startdate,
                               enddate,
                               methodname,
                               stationname,
                               sampling,
                               basetype,
                               gzip_flag=False,
                               itemname=itemname,
                               pointid=pointid)

        return jsonify(df.to_dict(orient='split'))


class remove_steps(Resource):

    def post(self):
        '''傅立叶去年变
        ---
        tags:
          - 数据库
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: upfile
            in: formData
            type: file
            required: true
            description: 利用傅立叶去年变方法提取的去年变后的时间序列（utf-8）

        responses:
          200:
            description: 从文件获取前兆时间序列数据
        '''
        uploaded_file = request.files['upfile']
        data = pd.read_json(uploaded_file, orient='split')
        df = self.remove_steps_by_logs(data)
        return jsonify(df.to_dict(orient='split'))

    def remove_steps_by_logs(self, df):
        df['STATIONID'] = df['STATIONID'].astype(str)
        df['POINTID'] = df['STATIONID'].str.slice(5)
        df['STATIONID'] = df['STATIONID'].str.slice(0, 5)
        print(df)
        step_dir = '../steps'
        step_files = glob.glob('../steps/*.txt')
        items = df['ITEMID'].unique()
        print(items)
        step_files = [os.path.basename(path) for path in step_files]
        for itemid in items:
            print(itemid)
            ts = df[df['ITEMID'] == itemid]['OBSVALUE']
            ts_0 = ts[-1]
            stationid = df.iloc[1, 0]
            pointid = df.iloc[1, 3]
            file = '_'.join([str(stationid), str(pointid), str(itemid)])
            file = '.'.join([file, 'txt'])
            if file in step_files:
                print(file)
                with open(os.path.join(step_dir, file), 'r') as f:
                    steps = f.read().split('\n')
                    if ' ' in steps:
                        steps.remove('')
                    ts.fillna(method='bfill', inplace=True)
                    ts_diff = ts.diff()
                    for step in steps:
                        print(step)
                        ts_diff.loc[step] = 0.0
                    ts = ts_diff.cumsum()
                    ts.fillna(method='bfill', inplace=True)
            ts_1 = ts[-1]
            ts = ts + (ts_0 - ts_1)
            df.loc[df['ITEMID'] == itemid, 'OBSVALUE'] = ts
        return df


class fourier_smoothing(Resource):

    def get(self):
        '''傅立叶去年变
        ---
        tags:
          - 数据库
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: database
            in: query
            type: string
            default: 'shandong-official'
            description: 数据库名称
          - name: stationname
            in: query
            type: string
            default: '马陵山'
            description: 台站名称
          - name: startdate
            in: query
            type: string
            default: '20150101'
            description: 起始时间
          - name: enddate
            in: query
            type: string
            default: '20211231'
            description: 结束时间
          - name: methodname
            in: query
            type: string
            default: '地电阻率'
            description: 测向名称，模糊查询
          - name: sampling
            in: query
            type: string
            default: '小时值'
            description: 采样率，模糊查询
          - name: basetype
            in: query
            type: string
            default: '预处理库'
            description: 数据类型：预处理库/原始库/产品库
          - name: itemname
            in: query
            type: string
            default: '直流单装置地电阻率观测东西向'
            description: 测向分量：精确匹配
          - name: pointid
            in: query
            type: string
            default: ''
            description: 测点号
          - name: gaps
            in: query
            type: string
            default: ''
            description: 缺数区间
          - name: returntype
            in: query
            type: boolean
            default: false
            description:  false:JSON数据表/true:JSON图片

        responses:
          200:
            description: 从数据库获取前兆时间序列数据
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('database',
                            type=str,
                            required=False,
                            location='args',
                            help='database')
        parser.add_argument('startdate',
                            type=str,
                            required=False,
                            location='args',
                            help='startdate')
        parser.add_argument('enddate',
                            type=str,
                            required=False,
                            location='args',
                            help='enddate')
        parser.add_argument('stationname',
                            type=str,
                            required=False,
                            location='args',
                            help='stationname')
        parser.add_argument('methodname',
                            type=str,
                            required=False,
                            location='args',
                            help='methodname')
        parser.add_argument('sampling',
                            type=str,
                            required=False,
                            location='args',
                            help='sampling')
        parser.add_argument('basetype',
                            type=str,
                            required=False,
                            location='args',
                            help='basetype')
        parser.add_argument('itemname',
                            type=str,
                            required=False,
                            location='args',
                            help='itemname')
        parser.add_argument('pointid',
                            type=str,
                            required=False,
                            location='args',
                            help='pointid')
        parser.add_argument('gaps',
                            type=str,
                            required=False,
                            location='args',
                            help='gaps')
        args = parser.parse_args()
        database = args['database']
        stationname = args['stationname']
        startdate = args['startdate']
        enddate = args['enddate']
        methodname = args['methodname']
        sampling = args['sampling']
        basetype = args['basetype']
        itemname = args['itemname']
        pointid = args['pointid']

        if not pointid:
            pointid = ''

        conn = tsf.conn_to_MySQL(database)

        df = tsf.fetching_data(conn,
                               startdate,
                               enddate,
                               methodname,
                               stationname,
                               sampling,
                               basetype,
                               gzip_flag=False,
                               itemname=itemname,
                               pointid=pointid)
        df = self.fourier_removing(df)

        return jsonify(df.to_dict(orient='split'))

    def post(self):
        '''傅立叶去年变
        ---
        tags:
          - 数据库
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: upfile
            in: formData
            type: file
            required: true
            description: 利用傅立叶去年变方法提取的去年变后的时间序列（utf-8）

        responses:
          200:
            description: 从文件获取前兆时间序列数据
        '''

        uploaded_file = request.files['upfile']
        data = pd.read_json(uploaded_file)
        df = self.fourier_removing(data)
        return jsonify(df.to_dict(orient='split'))

    @staticmethod
    def fourier(df, T=365):
        n = df.shape[0]
        df.fillna(inplace=True, method='pad', limit=181)
        w = pd.Series(range(0, n))
        tmp = pd.DataFrame()
        tmp['w'] = w
        tmp['o'] = df.values
        a = 2 / T * sum(tmp['o'] * cos(2 * pi * (tmp['w'] - n + T) / T))
        return a

    def fourier_removing(self, df):
        df.drop(['ITEMID', 'STATIONID', 'POINTID'], axis=1, inplace=True)
        df = df.resample('1D').mean(numeric_only=True)
        df['OBSVALUE'].fillna(inplace=True, method='ffill')
        df['fourier'] = df['OBSVALUE'].rolling(365).apply(self.fourier)
        df_1 = df['OBSVALUE'].iloc[:365 * 2]
        df_1_f = df_1[::-1].rolling(365).apply(self.fourier)
        df_1_f = df_1_f[::-1]
        df['fourier'].iloc[:365] = df_1_f.iloc[:365]
        df['fourier'].iloc[365:] = df['fourier'].iloc[365:] - df[
            'fourier'].iloc[365] + df['fourier'].iloc[364]

        df['residual'] = df['OBSVALUE'] - df['fourier']

        return df


class wavelets_trend(Resource):

    def post(self):
        '''利用小波分解方法提取数据变化趋势
        ---
        tags:
          - 小波分解
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: upfile
            in: formData
            type: file
            required: true
            description: 利用傅立叶去年变方法提取的去年变后的时间序列（utf-8）

        responses:
          200:
            description: 利用小波分解方法提取数据变化趋势
        '''
        uploaded_file = request.files['upfile']
        data = pd.read_json(uploaded_file, orient='split')
        data = data.tz_localize(None)
        df = self.wavelets_dec(data)

        return jsonify(df.to_dict(orient='split'))

    def wavelets_dec(self, df):
        import pywt

        wavelet_type = 'db10'
        wavelet_level = 10
        w = pywt.Wavelet(wavelet_type)
        df['residual'].fillna(inplace=True, method='ffill')
        signal = df['residual']
        coeffs = pywt.wavedec(signal,
                              wavelet=w,
                              mode='reflect',
                              level=wavelet_level)
        rec_sig_a = pywt.upcoef('a', coeffs[0], w, level=10, take=len(signal))
        df['trend'] = rec_sig_a
        df_trend = pd.DataFrame()
        df_trend['trend'] = df['trend']
        df_trend['residual'] = df['residual']
        fig = df_trend.plot()
        return df


class relative_range(Resource):

    def post(self):
        '''计算去年变时间序列的相对变化幅度
        ---
        tags:
          - 相对变化幅度
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: upfile
            in: formData
            type: file
            required: true
            description: 数据需包含趋势变化、残差时间序列（utf-8）

        responses:
          200:
            description: 计算去年变时间序列的相对变化幅度
        '''
        uploaded_file = request.files['upfile']
        data = pd.read_json(uploaded_file, orient='split')
        data = data.tz_localize(None)
        df = self.calculate_relative_range(data)

        return jsonify(df.to_dict(orient='split'))

    def calculate_relative_range(self, df):
        import pywt
        pd.options.plotting.backend = 'plotly'
        wavelet_type = 'db10'
        wavelet_level = 5
        w = pywt.Wavelet(wavelet_type)
        signal = df['residual']
        coeffs = pywt.wavedec(signal,
                              wavelet=w,
                              mode='reflect',
                              level=wavelet_level)
        rec_sig_0 = pywt.upcoef('a', coeffs[0], w, level=5, take=len(signal))
        rec_sig = rec_sig_0
        df['new_residual'] = rec_sig
        df['flag'] = False
        df['flag_0'] = df['new_residual'] - df['trend']
        df['flag_1'] = df['flag_0'].shift(1)
        df['flag_2'] = df['flag_1'] * df['flag_0']
        df.loc[df['flag_2'] < 0, 'flag'] = True
        df.drop(['flag_0', 'flag_1', 'flag_2'], axis=1, inplace=True)
        df['start'] = df['residual']
        df.iloc[0, 5] = True
        df.loc[df['flag'] == False, 'start'] = np.nan
        df.fillna(method='ffill', inplace=True)
        df['relative_range'] = (df['residual'] - df['start']) / df['start']
        df['relative_range'] = df['relative_range'] * 100
        fig = df['relative_range'].plot()
        # fig.show()

        return df


class annual_amplitude(Resource):

    def post(self):
        '''计算视电阻率年变化的年变化幅度
        ---
        tags:
          - 年变化幅度
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: upfile
            in: formData
            type: file
            required: true
            description: 数据需包含趋势、年变化时间序列（utf-8）

        responses:
          200:
            description: 计算视电阻率年变化的年变化幅度，利用希尔伯特变换方法
        '''
        uploaded_file = request.files['upfile']
        data = pd.read_json(uploaded_file, orient='split')
        data = data.tz_localize(None)
        df = self.res_hilbert(data)

        return jsonify(df.to_dict(orient='split'))

    @staticmethod
    def forward_std(df):
        df_r = pd.DataFrame()
        df_r['relative_range'] = df['relative_range']
        df_r['relative_range'] = df_r['relative_range'].pow(2).cumsum()
        df_r['k'] = df_r.reset_index().index + 1
        df_r['new_threshold'] = df_r['relative_range'] / df_r['k']
        df_r['new_threshold'] = df_r['new_threshold'].pow(1 / 2)

        return df_r['new_threshold'].mean()

    def res_hilbert(self, df):
        from scipy.signal import hilbert
        df['hilbert'] = np.abs(hilbert(df['fourier']))
        df['annual_1'] = ((df['hilbert']) * 100 / df['trend']).abs() / 2
        df['annual_1'][df.index[0]:df.index[180]] = df['annual_1'][
            df.index[181]]
        df['annual_1'][df.index[-180]:df.index[-1]] = df['annual_1'][
            df.index[-181]]
        df['annual_2'] = -df['annual_1']

        df_r = pd.DataFrame()
        df_r['relative_range'] = df['relative_range']
        df_r['relative_range'] = df_r['relative_range'].pow(2).cumsum()
        df_r['k'] = df_r.reset_index().index + 1
        df_r['new_threshold'] = df_r['relative_range'] / df_r['k']
        df_r['new_threshold'] = df_r['new_threshold'].pow(1 / 2)

        df['std_1'] = df_r['new_threshold'].mean() * 2.5
        df['std_2'] = -df_r['new_threshold'].mean() * 2.5

        return df


class resistivity_avam(Resource):

    def get(self):
        '''自适应变化幅度方法提取地电阻率异常
        ---
        tags:
          - 数据库
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: database
            in: query
            type: string
            default: 'shandong-official'
            description: 数据库名称
          - name: stationname
            in: query
            type: string
            default: '马陵山'
            description: 台站名称
          - name: startdate
            in: query
            type: string
            default: '20150101'
            description: 起始时间
          - name: enddate
            in: query
            type: string
            default: '20211231'
            description: 结束时间
          - name: methodname
            in: query
            type: string
            default: '地电阻率'
            description: 测向名称，模糊查询
          - name: sampling
            in: query
            type: string
            default: '小时值'
            description: 采样率，模糊查询
          - name: basetype
            in: query
            type: string
            default: '预处理库'
            description: 数据类型：预处理库/原始库/产品库
          - name: itemname
            in: query
            type: string
            default: '直流单装置地电阻率观测东西向'
            description: 测向分量：精确匹配
          - name: pointid
            in: query
            type: string
            default: ''
            description: 测点号，台站只有一套仪器不需要填
          - name: gaps
            in: query
            type: string
            default: ''
            description: 缺数区间
          - name: returntype
            in: query
            type: boolean
            default: false
            description:  false:JSON数据表/true:JSON图片

        responses:
          200:
            description: 自适应变化幅度方法提取地电阻率异常
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('database',
                            type=str,
                            required=False,
                            location='args',
                            help='database')
        parser.add_argument('startdate',
                            type=str,
                            required=False,
                            location='args',
                            help='startdate')
        parser.add_argument('enddate',
                            type=str,
                            required=False,
                            location='args',
                            help='enddate')
        parser.add_argument('stationname',
                            type=str,
                            required=False,
                            location='args',
                            help='stationname')
        parser.add_argument('methodname',
                            type=str,
                            required=False,
                            location='args',
                            help='methodname')
        parser.add_argument('sampling',
                            type=str,
                            required=False,
                            location='args',
                            help='sampling')
        parser.add_argument('basetype',
                            type=str,
                            required=False,
                            location='args',
                            help='basetype')
        parser.add_argument('itemname',
                            type=str,
                            required=False,
                            location='args',
                            help='itemname')
        parser.add_argument('pointid',
                            type=str,
                            required=False,
                            location='args',
                            help=u'台站只有一套仪器不需要填')
        parser.add_argument('gaps',
                            type=str,
                            required=False,
                            location='args',
                            help='gaps')
        parser.add_argument('returntype',
                            type=str,
                            required=False,
                            location='args',
                            help='return type')
        args = parser.parse_args()
        database = args['database']
        stationname = args['stationname']
        startdate = args['startdate']
        enddate = args['enddate']
        methodname = args['methodname']
        sampling = args['sampling']
        basetype = args['basetype']
        itemname = args['itemname']
        pointid = args['pointid']

        print(f'point id:{pointid}')
        if not pointid:
            pointid = ''

        conn = tsf.conn_to_MySQL(database)

        df = tsf.fetching_data(conn,
                               startdate,
                               enddate,
                               methodname,
                               stationname,
                               sampling,
                               basetype,
                               gzip_flag=False,
                               itemname=itemname,
                               pointid=pointid)

        # df = df.groupby(['ITEMID', 'STATIONID']).resample('1D').median(numeric_only=True)
        # df.reset_index(inplace=True)
        # df.set_index('STARTDATE', inplace=True)

        rs = remove_steps()
        df = rs.remove_steps_by_logs(df)
        fs = fourier_smoothing()
        df = fs.fourier_removing(df)
        wt = wavelets_trend()
        df = wt.wavelets_dec(df)
        rr = relative_range()
        df = rr.calculate_relative_range(df)
        aa = annual_amplitude()
        df = aa.res_hilbert(df)
        return jsonify(df.to_dict(orient='split'))

    def post(self):
        '''自适应变化幅度方法提取地电阻率异常
        ---
        tags:
          - 数据库
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: upfile
            in: formData
            type: file
            required: true
            description: 利用自适应变化幅度方法提取地电阻率异常

        responses:
          200:
            description: 自适应变化幅度方法提取地电阻率异常
        '''

        uploaded_file = request.files['upfile']
        df = pd.read_json(uploaded_file)

        fs = fourier_smoothing()
        df = fs.fourier_removing(df)
        wt = wavelets_trend()
        df = wt.wavelets_dec(df)
        rr = relative_range()
        df = rr.calculate_relative_range(df)
        aa = annual_amplitude()
        df = aa.res_hilbert(df)
        return jsonify(df.to_dict(orient='split'))


class resistivity_std_cn(Resource):

    def get(self):
        '''标准差判定
        ---
        tags:
          - 电磁
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: database
            in: query
            type: string
            default: 'shandong-official'
            description: 数据库名称
          - name: methodname
            in: query
            type: string
            default: '地电阻率'
            description: 方法名称
          - name: window
            in: query
            type: number
            default: 365
            description: 监视窗长
          - name: scale
            in: query
            type: number
            default: 2.0
            description: 标准差倍数
        responses:
          200:
            description: 用于判定标准差
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('database',
                            type=str,
                            required=False,
                            location='args',
                            help='database')
        parser.add_argument('methodname',
                            type=str,
                            required=False,
                            location='args',
                            help='methodname')
        parser.add_argument('window',
                            type=int,
                            location='args',
                            required=False,
                            help='monitoring window')
        parser.add_argument('scale',
                            type=float,
                            location='args',
                            required=False,
                            help='scale')
        args = parser.parse_args()
        database = args['database']
        methodname = args['methodname']
        monitoring_window = args['window']
        scale = args['scale']
        stations = self.list_stations_by_item(methodname=methodname,
                                              database=database)
        df = self.china_resistivity_std(stations,
                                        database=database,
                                        startdate='20160101',
                                        monitoring_window=monitoring_window,
                                        scale=scale)

        return jsonify(df.to_dict(orient='split'))

    def list_stations_by_item_sqlalchemy(self,
                                         methodname='地电阻率',
                                         database='shandong-official'):
        '''
        Oracle数据库自动映射的表名、列名为小写，
        MySQL数据库自动映射的表名、列名为大写。
        Shit!
        '''
        # connect to database
        os.environ['NLS_LANG'] = 'AMERICAN_AMERICA.ZHS16GBK'
        config = configparser.ConfigParser()
        config.read('default.conf')
        url = sqlalchemy.engine.URL(
            config[database]['DRIVER'], config[database]['USERNAME'],
            parse.quote_plus(config[database]['PASSWORD']),
            config[database]['HOST'], config[database]['PORT'],
            config[database]['TNSNAME'])
        engine = create_engine(url)
        metadata = MetaData()
        metadata.reflect(
            engine,
            schema='qzdata',
            only=['qz_dict_stations', 'qz_dict_stationitems', 'qz_dict_items'])
        Base = automap_base(metadata=metadata)
        Base.prepare()
        qz_dict_stations = Base.classes.qz_dict_stations
        qz_dict_stationitems = Base.classes.qz_dict_stationitems
        qz_dict_items = Base.classes.qz_dict_items
        Session = sessionmaker(bind=engine)
        session = Session()
        sql = session.query(qz_dict_stations.stationid, qz_dict_stations.stationname).\
                        distinct(qz_dict_stations.stationid).\
                            join(qz_dict_stationitems, qz_dict_stations.stationid==qz_dict_stationitems.stationid).\
                                join(qz_dict_items, qz_dict_stationitems.itemid==qz_dict_items.itemid).\
                                    filter(qz_dict_items.itemname.like(f'%{methodname}%')).statement
        stations = pd.read_sql(sql=sql, con=engine)

        return stations

    def list_stations_by_item(self,
                              database='shandong-official',
                              methodname='地电阻率'):

        os.environ['NLS_LANG'] = 'AMERICAN_AMERICA.ZHS16GBK'

        conn = tsf.conn_to_MySQL(database)

        sql = 'SELECT METHODID FROM QZDATA.QZ_DICT_METHODS WHERE METHODNAME LIKE %s'
        df = pd.read_sql(sql, con=conn, params={'%%' + methodname + '%%'})
        methodid = df['METHODID'][0]
        sql = "SELECT DISTINCT A.STATIONID, A.STATIONNAME, B.POINTID FROM " \
            "QZDATA.QZ_DICT_STATIONS A, QZDATA.QZ_DICT_STATIONITEMS B " \
            "WHERE A.STATIONID=B.STATIONID AND SUBSTR(B.ITEMID,1,3)=%s ORDER BY A.STATIONID"
        df = pd.read_sql(sql, con=conn, params={methodid})

        return df

    def china_resistivity_std(self,
                              stations,
                              database='shandong-official',
                              startdate='20160101',
                              monitoring_window=365,
                              scale=2.0):
        '''目前没有考虑点号，对与同台多测点的台站会有问题，可以考虑加一重点号循环'''
        # connect to database
        os.environ['NLS_LANG'] = 'AMERICAN_AMERICA.ZHS16GBK'

        conn = tsf.conn_to_MySQL(database)

        itemid = ['3211', '3212', '3213', '3214']
        ts_df = pd.DataFrame()
        df_std = pd.DataFrame()
        monitoring_startdate = (
            dt.datetime.today() +
            dt.timedelta(-monitoring_window)).strftime('%Y%m%d')
        enddate = dt.datetime.today().strftime('%Y%m%d')
        for index, row in stations.iterrows():
            station = row['stationname']
            pointid = row['pointid']
            print(station)
            try:
                ts_df = tsf.fetching_data(conn,
                                          startdate,
                                          enddate,
                                          '地电阻率观测',
                                          station,
                                          '小时值',
                                          '预处理库',
                                          itemname='地电阻率观测',
                                          gzip_flag=False)
                items = ts_df['ITEMID'].unique()
                itemid = list(set(items) & set(itemid))
                for item in itemid:
                    df = ts_df[ts_df['ITEMID'] == item].resample('1d').median()
                    upper = df['OBSVALUE'].mean(
                    ) + df['OBSVALUE'].std() * scale
                    lower = df['OBSVALUE'].mean(
                    ) - df['OBSVALUE'].std() * scale
                    count = len(df.loc[(df.index >= monitoring_startdate) &
                                       (df.index < enddate) &
                                       ((df['OBSVALUE'] < lower) |
                                        (df['OBSVALUE'] > upper)), 'OBSVALUE'])
                    if count:
                        tmp = pd.DataFrame(
                            [[station, item, count]],
                            columns=['station', 'itemid', 'count_days'])
                        df_std = pd.concat([df_std, tmp])
            except BaseException:
                continue

        return df_std


api.add_resource(amplitude_ratio, '/amplitude_ratio')
api.add_resource(load_unload_ratio, '/load_unload_ratio')
api.add_resource(eleAzimuth, '/ele_Azimuth')
api.add_resource(gantt_plot, '/gantt_plot')
api.add_resource(fetching_data_API, '/fetching_data_API')
api.add_resource(fourier_smoothing, '/fourier_smoothing')
api.add_resource(wavelets_trend, '/wavelets_trend')
api.add_resource(relative_range, '/relative_range')
api.add_resource(annual_amplitude, '/annual_amplitude')
api.add_resource(resistivity_avam, '/resistivity_avam')
api.add_resource(geomagnetic_harmonic_ratio, '/geomagnetic_harmonic_ratio')
api.add_resource(resistivity_std_cn, '/resistivity_std')
api.add_resource(remove_steps, '/remove_steps')
