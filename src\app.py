import os
import sys
import glob
import cx_<PERSON>
from urllib import parse
import datetime as dt
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import plotly.express as px
import plotly.graph_objs as go
from plotly.subplots import make_subplots
import razorback as rb
from scipy import signal
from scipy.signal import argrelmax, argrelmin
from flasgger import Swagger
from flask import Flask, request, jsonify, make_response
from flask_cors import CORS
from flask_restful import Api
from addereq import fetching as tsf
from matplotlib import rcParams
from matplotlib.pyplot import cm
from numpy import pi, sin, cos, sqrt, arctan
from numpy.fft import fft
from pandas.plotting import register_matplotlib_converters

from .api import amplitude_ratio, load_unload_ratio, eleAzimuth, fourier_smoothing, wavelets_trend, relative_range, annual_amplitude, resistivity_avam, geomagnetic_harmonic_ratio, resistivity_std_cn, remove_steps

register_matplotlib_converters()

app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False
CORS(app, supports_credentials=True)
api = Api(app)

UPLOAD_PATH = os.path.abspath('.')

swagger_config = Swagger.DEFAULT_CONFIG
swagger_config['title'] = '电磁学科时间序列处理接口'
swagger_config['description'] = '电磁学科时间序列处理方法，差分、地磁加卸载响应比、地电场优势方位角等'
swagger_config['host'] = '0.0.0.0:8888'
swagger_config['optional_fields'] = ['components']
swagger_config['version'] = ['1.0.0']

swagger = Swagger(app, config=swagger_config)


@app.route('/', methods=['POST', 'GET'])
def index():
    '''电磁学科时间序列数据处理API
    
    tags:
      - Welcome to using online calculator API.
    responses:
      200:
        description:  电磁学科时间序列数据处理API
    '''

    return '欢迎使用电磁学科时间序列数据处理API！'


api.add_resource(amplitude_ratio, '/amplitude_ratio')
api.add_resource(load_unload_ratio, '/load_unload_ratio')
api.add_resource(eleAzimuth, '/ele_Azimuth')
api.add_resource(fourier_smoothing, '/fourier_smoothing')
api.add_resource(wavelets_trend, '/wavelets_trend')
api.add_resource(relative_range, '/relative_range')
api.add_resource(annual_amplitude, '/annual_amplitude')
api.add_resource(resistivity_avam, '/resistivity_avam')
api.add_resource(geomagnetic_harmonic_ratio, '/geomagnetic_harmonic_ratio')
api.add_resource(resistivity_std_cn, '/resistivity_std')
api.add_resource(remove_steps, '/remove_steps')

app.run()
