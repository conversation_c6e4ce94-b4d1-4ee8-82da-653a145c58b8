{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "import glob\n", "import numpy as np\n", "import urllib\n", "pd.options.plotting.backend = 'plotly'"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["params = urllib.parse.urlencode({'database': 'shandong-official',\n", "                                'stationname': '马陵山',\n", "                                'startdate': '20180101',\n", "                                'enddate': '20230531',\n", "                                'methodname': '电阻率',\n", "                                'itemname': '直流单装置地电阻率观测北南向',\n", "                                'sampling': '小时值',\n", "                                'basetype': '预处理库'})\n", "\n", "url = 'http://192.168.31.38:8888/fetching_data_API?%s' % params\n", "\n", "df = pd.read_json(url, orient='split')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["df['POINTID'] = df['STATIONID'].str.slice(5)\n", "df['STATIONID'] = df['STATIONID'].str.slice(0, 5)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>STATIONID</th>\n", "      <th>ITEMID</th>\n", "      <th>OBSVALUE</th>\n", "      <th>POINTID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2018-01-01 00:00:00+00:00</th>\n", "      <td>37005</td>\n", "      <td>3211</td>\n", "      <td>49.270000</td>\n", "      <td>E</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-01 01:00:00+00:00</th>\n", "      <td>37005</td>\n", "      <td>3211</td>\n", "      <td>49.240002</td>\n", "      <td>E</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-01 02:00:00+00:00</th>\n", "      <td>37005</td>\n", "      <td>3211</td>\n", "      <td>49.169998</td>\n", "      <td>E</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-01 03:00:00+00:00</th>\n", "      <td>37005</td>\n", "      <td>3211</td>\n", "      <td>49.189999</td>\n", "      <td>E</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-01 04:00:00+00:00</th>\n", "      <td>37005</td>\n", "      <td>3211</td>\n", "      <td>49.200001</td>\n", "      <td>E</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-30 19:00:00+00:00</th>\n", "      <td>37005</td>\n", "      <td>3211</td>\n", "      <td>48.910000</td>\n", "      <td>E</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-30 20:00:00+00:00</th>\n", "      <td>37005</td>\n", "      <td>3211</td>\n", "      <td>48.939999</td>\n", "      <td>E</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-30 21:00:00+00:00</th>\n", "      <td>37005</td>\n", "      <td>3211</td>\n", "      <td>49.000000</td>\n", "      <td>E</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-30 22:00:00+00:00</th>\n", "      <td>37005</td>\n", "      <td>3211</td>\n", "      <td>49.060001</td>\n", "      <td>E</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-30 23:00:00+00:00</th>\n", "      <td>37005</td>\n", "      <td>3211</td>\n", "      <td>49.060001</td>\n", "      <td>E</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>47424 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                          STATIONID  ITEMID   OBSVALUE POINTID\n", "2018-01-01 00:00:00+00:00     37005    3211  49.270000       E\n", "2018-01-01 01:00:00+00:00     37005    3211  49.240002       E\n", "2018-01-01 02:00:00+00:00     37005    3211  49.169998       E\n", "2018-01-01 03:00:00+00:00     37005    3211  49.189999       E\n", "2018-01-01 04:00:00+00:00     37005    3211  49.200001       E\n", "...                             ...     ...        ...     ...\n", "2023-05-30 19:00:00+00:00     37005    3211  48.910000       E\n", "2023-05-30 20:00:00+00:00     37005    3211  48.939999       E\n", "2023-05-30 21:00:00+00:00     37005    3211  49.000000       E\n", "2023-05-30 22:00:00+00:00     37005    3211  49.060001       E\n", "2023-05-30 23:00:00+00:00     37005    3211  49.060001       E\n", "\n", "[47424 rows x 4 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def remove_steps_by_logs(df):\n", "    step_dir = '/Users/<USER>/Work.localized/Python/resistivity_2023/res_remove/res_steps'\n", "    step_files = glob.glob('/Users/<USER>/Work.localized/Python/resistivity_2023/res_remove/res_steps/*.txt')\n", "    items = df['ITEMID'].unique()\n", "    print(items)\n", "    step_files = [os.path.basename(path) for path in step_files]\n", "    for itemid in items:\n", "        print(itemid)\n", "        ts = df[df['ITEMID']==itemid]['OBSVALUE']\n", "        ts_0 = ts[-1]\n", "        stationid = df.iloc[1, 0]\n", "        pointid = df.iloc[1, 3]\n", "        file = '_'.join([str(stationid), str(pointid), str(itemid)])\n", "        file = '.'.join([file, 'txt'])\n", "        if file in step_files:\n", "            print(file)\n", "            with open(os.path.join(step_dir, file), 'r') as f:\n", "                steps = f.read().split('\\n')\n", "                if ' ' in steps:\n", "                    steps.remove('')\n", "                ts.fillna(method='bfill', inplace=True)\n", "                ts_diff = ts.diff()\n", "                for step in steps:\n", "                    print(step)\n", "                    ts_diff.loc[step] = 0.0\n", "                ts = ts_diff.cumsum()\n", "                ts.fillna(method='bfill', inplace=True)\n", "        ts_1 = ts[-1]\n", "        ts = ts + (ts_0 - ts_1)\n", "        df.loc[df['ITEMID']==itemid, 'OBSVALUE'] = ts\n", "    return df"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import resistivity_avam_2022 as ra"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["rs = ra.remove_steps()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[3211]\n", "3211\n", "37005_E_3211.txt\n", "20190503\n", "20190504\n", "20190505\n", "20190506\n", "20191028\n", "20191029\n", "20191030\n", "20191031\n", "\n"]}], "source": ["df1 = rs.remove_steps_by_logs(df)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "variable=OBSVALUE<br>index=%{x}<br>value=%{y}<extra></extra>", "legendgroup": "OBSVALUE", "line": {"color": "#636efa", "dash": "solid"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "OBSVALUE", "showlegend": true, "type": "scattergl", "x": ["2018-01-01T00:00:00+00:00", "2018-01-02T00:00:00+00:00", "2018-01-03T00:00:00+00:00", "2018-01-04T00:00:00+00:00", "2018-01-05T00:00:00+00:00", "2018-01-06T00:00:00+00:00", "2018-01-07T00:00:00+00:00", "2018-01-08T00:00:00+00:00", "2018-01-09T00:00:00+00:00", "2018-01-10T00:00:00+00:00", "2018-01-11T00:00:00+00:00", "2018-01-12T00:00:00+00:00", "2018-01-13T00:00:00+00:00", "2018-01-14T00:00:00+00:00", "2018-01-15T00:00:00+00:00", "2018-01-16T00:00:00+00:00", "2018-01-17T00:00:00+00:00", "2018-01-18T00:00:00+00:00", "2018-01-19T00:00:00+00:00", "2018-01-20T00:00:00+00:00", "2018-01-21T00:00:00+00:00", "2018-01-22T00:00:00+00:00", "2018-01-23T00:00:00+00:00", "2018-01-24T00:00:00+00:00", "2018-01-25T00:00:00+00:00", "2018-01-26T00:00:00+00:00", "2018-01-27T00:00:00+00:00", "2018-01-28T00:00:00+00:00", "2018-01-29T00:00:00+00:00", "2018-01-30T00:00:00+00:00", "2018-01-31T00:00:00+00:00", "2018-02-01T00:00:00+00:00", "2018-02-02T00:00:00+00:00", "2018-02-03T00:00:00+00:00", "2018-02-04T00:00:00+00:00", "2018-02-05T00:00:00+00:00", "2018-02-06T00:00:00+00:00", "2018-02-07T00:00:00+00:00", "2018-02-08T00:00:00+00:00", "2018-02-09T00:00:00+00:00", "2018-02-10T00:00:00+00:00", "2018-02-11T00:00:00+00:00", "2018-02-12T00:00:00+00:00", "2018-02-13T00:00:00+00:00", "2018-02-14T00:00:00+00:00", "2018-02-15T00:00:00+00:00", "2018-02-16T00:00:00+00:00", "2018-02-17T00:00:00+00:00", "2018-02-18T00:00:00+00:00", "2018-02-19T00:00:00+00:00", "2018-02-20T00:00:00+00:00", "2018-02-21T00:00:00+00:00", "2018-02-22T00:00:00+00:00", "2018-02-23T00:00:00+00:00", "2018-02-24T00:00:00+00:00", "2018-02-25T00:00:00+00:00", "2018-02-26T00:00:00+00:00", "2018-02-27T00:00:00+00:00", "2018-02-28T00:00:00+00:00", "2018-03-01T00:00:00+00:00", "2018-03-02T00:00:00+00:00", "2018-03-03T00:00:00+00:00", "2018-03-04T00:00:00+00:00", "2018-03-05T00:00:00+00:00", "2018-03-06T00:00:00+00:00", "2018-03-07T00:00:00+00:00", "2018-03-08T00:00:00+00:00", "2018-03-09T00:00:00+00:00", "2018-03-10T00:00:00+00:00", "2018-03-11T00:00:00+00:00", "2018-03-12T00:00:00+00:00", "2018-03-13T00:00:00+00:00", "2018-03-14T00:00:00+00:00", "2018-03-15T00:00:00+00:00", "2018-03-16T00:00:00+00:00", "2018-03-17T00:00:00+00:00", "2018-03-18T00:00:00+00:00", "2018-03-19T00:00:00+00:00", "2018-03-20T00:00:00+00:00", "2018-03-21T00:00:00+00:00", "2018-03-22T00:00:00+00:00", "2018-03-23T00:00:00+00:00", "2018-03-24T00:00:00+00:00", "2018-03-25T00:00:00+00:00", "2018-03-26T00:00:00+00:00", "2018-03-27T00:00:00+00:00", "2018-03-28T00:00:00+00:00", "2018-03-29T00:00:00+00:00", "2018-03-30T00:00:00+00:00", "2018-03-31T00:00:00+00:00", "2018-04-01T00:00:00+00:00", "2018-04-02T00:00:00+00:00", "2018-04-03T00:00:00+00:00", "2018-04-04T00:00:00+00:00", "2018-04-05T00:00:00+00:00", "2018-04-06T00:00:00+00:00", "2018-04-07T00:00:00+00:00", "2018-04-08T00:00:00+00:00", "2018-04-09T00:00:00+00:00", "2018-04-10T00:00:00+00:00", "2018-04-11T00:00:00+00:00", "2018-04-12T00:00:00+00:00", "2018-04-13T00:00:00+00:00", "2018-04-14T00:00:00+00:00", "2018-04-15T00:00:00+00:00", "2018-04-16T00:00:00+00:00", "2018-04-17T00:00:00+00:00", "2018-04-18T00:00:00+00:00", "2018-04-19T00:00:00+00:00", "2018-04-20T00:00:00+00:00", "2018-04-21T00:00:00+00:00", "2018-04-22T00:00:00+00:00", "2018-04-23T00:00:00+00:00", "2018-04-24T00:00:00+00:00", "2018-04-25T00:00:00+00:00", "2018-04-26T00:00:00+00:00", "2018-04-27T00:00:00+00:00", "2018-04-28T00:00:00+00:00", "2018-04-29T00:00:00+00:00", "2018-04-30T00:00:00+00:00", "2018-05-01T00:00:00+00:00", "2018-05-02T00:00:00+00:00", "2018-05-03T00:00:00+00:00", "2018-05-04T00:00:00+00:00", "2018-05-05T00:00:00+00:00", "2018-05-06T00:00:00+00:00", "2018-05-07T00:00:00+00:00", "2018-05-08T00:00:00+00:00", "2018-05-09T00:00:00+00:00", "2018-05-10T00:00:00+00:00", "2018-05-11T00:00:00+00:00", "2018-05-12T00:00:00+00:00", "2018-05-13T00:00:00+00:00", "2018-05-14T00:00:00+00:00", "2018-05-15T00:00:00+00:00", "2018-05-16T00:00:00+00:00", "2018-05-17T00:00:00+00:00", "2018-05-18T00:00:00+00:00", "2018-05-19T00:00:00+00:00", "2018-05-20T00:00:00+00:00", "2018-05-21T00:00:00+00:00", "2018-05-22T00:00:00+00:00", "2018-05-23T00:00:00+00:00", "2018-05-24T00:00:00+00:00", "2018-05-25T00:00:00+00:00", "2018-05-26T00:00:00+00:00", "2018-05-27T00:00:00+00:00", "2018-05-28T00:00:00+00:00", "2018-05-29T00:00:00+00:00", "2018-05-30T00:00:00+00:00", "2018-05-31T00:00:00+00:00", "2018-06-01T00:00:00+00:00", "2018-06-02T00:00:00+00:00", "2018-06-03T00:00:00+00:00", "2018-06-04T00:00:00+00:00", "2018-06-05T00:00:00+00:00", "2018-06-06T00:00:00+00:00", "2018-06-07T00:00:00+00:00", "2018-06-08T00:00:00+00:00", "2018-06-09T00:00:00+00:00", "2018-06-10T00:00:00+00:00", "2018-06-11T00:00:00+00:00", "2018-06-12T00:00:00+00:00", "2018-06-13T00:00:00+00:00", "2018-06-14T00:00:00+00:00", "2018-06-15T00:00:00+00:00", "2018-06-16T00:00:00+00:00", "2018-06-17T00:00:00+00:00", "2018-06-18T00:00:00+00:00", "2018-06-19T00:00:00+00:00", "2018-06-20T00:00:00+00:00", "2018-06-21T00:00:00+00:00", "2018-06-22T00:00:00+00:00", "2018-06-23T00:00:00+00:00", "2018-06-24T00:00:00+00:00", "2018-06-25T00:00:00+00:00", "2018-06-26T00:00:00+00:00", "2018-06-27T00:00:00+00:00", "2018-06-28T00:00:00+00:00", "2018-06-29T00:00:00+00:00", "2018-06-30T00:00:00+00:00", "2018-07-01T00:00:00+00:00", "2018-07-02T00:00:00+00:00", "2018-07-03T00:00:00+00:00", "2018-07-04T00:00:00+00:00", "2018-07-05T00:00:00+00:00", "2018-07-06T00:00:00+00:00", "2018-07-07T00:00:00+00:00", "2018-07-08T00:00:00+00:00", "2018-07-09T00:00:00+00:00", "2018-07-10T00:00:00+00:00", "2018-07-11T00:00:00+00:00", "2018-07-12T00:00:00+00:00", "2018-07-13T00:00:00+00:00", "2018-07-14T00:00:00+00:00", "2018-07-15T00:00:00+00:00", "2018-07-16T00:00:00+00:00", "2018-07-17T00:00:00+00:00", "2018-07-18T00:00:00+00:00", "2018-07-19T00:00:00+00:00", "2018-07-20T00:00:00+00:00", "2018-07-21T00:00:00+00:00", "2018-07-22T00:00:00+00:00", "2018-07-23T00:00:00+00:00", "2018-07-24T00:00:00+00:00", "2018-07-25T00:00:00+00:00", "2018-07-26T00:00:00+00:00", "2018-07-27T00:00:00+00:00", "2018-07-28T00:00:00+00:00", "2018-07-29T00:00:00+00:00", "2018-07-30T00:00:00+00:00", "2018-07-31T00:00:00+00:00", "2018-08-01T00:00:00+00:00", "2018-08-02T00:00:00+00:00", "2018-08-03T00:00:00+00:00", "2018-08-04T00:00:00+00:00", "2018-08-05T00:00:00+00:00", "2018-08-06T00:00:00+00:00", "2018-08-07T00:00:00+00:00", "2018-08-08T00:00:00+00:00", "2018-08-09T00:00:00+00:00", "2018-08-10T00:00:00+00:00", "2018-08-11T00:00:00+00:00", "2018-08-12T00:00:00+00:00", "2018-08-13T00:00:00+00:00", "2018-08-14T00:00:00+00:00", "2018-08-15T00:00:00+00:00", "2018-08-16T00:00:00+00:00", "2018-08-17T00:00:00+00:00", "2018-08-18T00:00:00+00:00", "2018-08-19T00:00:00+00:00", "2018-08-20T00:00:00+00:00", "2018-08-21T00:00:00+00:00", "2018-08-22T00:00:00+00:00", "2018-08-23T00:00:00+00:00", "2018-08-24T00:00:00+00:00", "2018-08-25T00:00:00+00:00", "2018-08-26T00:00:00+00:00", "2018-08-27T00:00:00+00:00", "2018-08-28T00:00:00+00:00", "2018-08-29T00:00:00+00:00", "2018-08-30T00:00:00+00:00", "2018-08-31T00:00:00+00:00", "2018-09-01T00:00:00+00:00", "2018-09-02T00:00:00+00:00", "2018-09-03T00:00:00+00:00", "2018-09-04T00:00:00+00:00", "2018-09-05T00:00:00+00:00", "2018-09-06T00:00:00+00:00", "2018-09-07T00:00:00+00:00", "2018-09-08T00:00:00+00:00", "2018-09-09T00:00:00+00:00", "2018-09-10T00:00:00+00:00", "2018-09-11T00:00:00+00:00", "2018-09-12T00:00:00+00:00", "2018-09-13T00:00:00+00:00", "2018-09-14T00:00:00+00:00", "2018-09-15T00:00:00+00:00", "2018-09-16T00:00:00+00:00", "2018-09-17T00:00:00+00:00", "2018-09-18T00:00:00+00:00", "2018-09-19T00:00:00+00:00", "2018-09-20T00:00:00+00:00", "2018-09-21T00:00:00+00:00", "2018-09-22T00:00:00+00:00", "2018-09-23T00:00:00+00:00", "2018-09-24T00:00:00+00:00", "2018-09-25T00:00:00+00:00", "2018-09-26T00:00:00+00:00", "2018-09-27T00:00:00+00:00", "2018-09-28T00:00:00+00:00", "2018-09-29T00:00:00+00:00", "2018-09-30T00:00:00+00:00", "2018-10-01T00:00:00+00:00", "2018-10-02T00:00:00+00:00", "2018-10-03T00:00:00+00:00", "2018-10-04T00:00:00+00:00", "2018-10-05T00:00:00+00:00", "2018-10-06T00:00:00+00:00", "2018-10-07T00:00:00+00:00", "2018-10-08T00:00:00+00:00", "2018-10-09T00:00:00+00:00", "2018-10-10T00:00:00+00:00", "2018-10-11T00:00:00+00:00", "2018-10-12T00:00:00+00:00", "2018-10-13T00:00:00+00:00", "2018-10-14T00:00:00+00:00", "2018-10-15T00:00:00+00:00", "2018-10-16T00:00:00+00:00", "2018-10-17T00:00:00+00:00", "2018-10-18T00:00:00+00:00", "2018-10-19T00:00:00+00:00", "2018-10-20T00:00:00+00:00", "2018-10-21T00:00:00+00:00", "2018-10-22T00:00:00+00:00", "2018-10-23T00:00:00+00:00", "2018-10-24T00:00:00+00:00", "2018-10-25T00:00:00+00:00", "2018-10-26T00:00:00+00:00", "2018-10-27T00:00:00+00:00", "2018-10-28T00:00:00+00:00", "2018-10-29T00:00:00+00:00", "2018-10-30T00:00:00+00:00", "2018-10-31T00:00:00+00:00", "2018-11-01T00:00:00+00:00", "2018-11-02T00:00:00+00:00", "2018-11-03T00:00:00+00:00", "2018-11-04T00:00:00+00:00", "2018-11-05T00:00:00+00:00", "2018-11-06T00:00:00+00:00", "2018-11-07T00:00:00+00:00", "2018-11-08T00:00:00+00:00", "2018-11-09T00:00:00+00:00", "2018-11-10T00:00:00+00:00", "2018-11-11T00:00:00+00:00", "2018-11-12T00:00:00+00:00", "2018-11-13T00:00:00+00:00", "2018-11-14T00:00:00+00:00", "2018-11-15T00:00:00+00:00", "2018-11-16T00:00:00+00:00", "2018-11-17T00:00:00+00:00", "2018-11-18T00:00:00+00:00", "2018-11-19T00:00:00+00:00", "2018-11-20T00:00:00+00:00", "2018-11-21T00:00:00+00:00", "2018-11-22T00:00:00+00:00", "2018-11-23T00:00:00+00:00", "2018-11-24T00:00:00+00:00", "2018-11-25T00:00:00+00:00", "2018-11-26T00:00:00+00:00", "2018-11-27T00:00:00+00:00", "2018-11-28T00:00:00+00:00", "2018-11-29T00:00:00+00:00", "2018-11-30T00:00:00+00:00", "2018-12-01T00:00:00+00:00", "2018-12-02T00:00:00+00:00", "2018-12-03T00:00:00+00:00", "2018-12-04T00:00:00+00:00", "2018-12-05T00:00:00+00:00", "2018-12-06T00:00:00+00:00", "2018-12-07T00:00:00+00:00", "2018-12-08T00:00:00+00:00", "2018-12-09T00:00:00+00:00", "2018-12-10T00:00:00+00:00", "2018-12-11T00:00:00+00:00", "2018-12-12T00:00:00+00:00", "2018-12-13T00:00:00+00:00", "2018-12-14T00:00:00+00:00", "2018-12-15T00:00:00+00:00", "2018-12-16T00:00:00+00:00", "2018-12-17T00:00:00+00:00", "2018-12-18T00:00:00+00:00", "2018-12-19T00:00:00+00:00", "2018-12-20T00:00:00+00:00", "2018-12-21T00:00:00+00:00", "2018-12-22T00:00:00+00:00", "2018-12-23T00:00:00+00:00", "2018-12-24T00:00:00+00:00", "2018-12-25T00:00:00+00:00", "2018-12-26T00:00:00+00:00", "2018-12-27T00:00:00+00:00", "2018-12-28T00:00:00+00:00", "2018-12-29T00:00:00+00:00", "2018-12-30T00:00:00+00:00", "2018-12-31T00:00:00+00:00", "2019-01-01T00:00:00+00:00", "2019-01-02T00:00:00+00:00", "2019-01-03T00:00:00+00:00", "2019-01-04T00:00:00+00:00", "2019-01-05T00:00:00+00:00", "2019-01-06T00:00:00+00:00", "2019-01-07T00:00:00+00:00", "2019-01-08T00:00:00+00:00", "2019-01-09T00:00:00+00:00", "2019-01-10T00:00:00+00:00", "2019-01-11T00:00:00+00:00", "2019-01-12T00:00:00+00:00", "2019-01-13T00:00:00+00:00", "2019-01-14T00:00:00+00:00", "2019-01-15T00:00:00+00:00", "2019-01-16T00:00:00+00:00", "2019-01-17T00:00:00+00:00", "2019-01-18T00:00:00+00:00", "2019-01-19T00:00:00+00:00", "2019-01-20T00:00:00+00:00", "2019-01-21T00:00:00+00:00", "2019-01-22T00:00:00+00:00", "2019-01-23T00:00:00+00:00", "2019-01-24T00:00:00+00:00", "2019-01-25T00:00:00+00:00", "2019-01-26T00:00:00+00:00", "2019-01-27T00:00:00+00:00", "2019-01-28T00:00:00+00:00", "2019-01-29T00:00:00+00:00", "2019-01-30T00:00:00+00:00", "2019-01-31T00:00:00+00:00", "2019-02-01T00:00:00+00:00", "2019-02-02T00:00:00+00:00", "2019-02-03T00:00:00+00:00", "2019-02-04T00:00:00+00:00", "2019-02-05T00:00:00+00:00", "2019-02-06T00:00:00+00:00", "2019-02-07T00:00:00+00:00", "2019-02-08T00:00:00+00:00", "2019-02-09T00:00:00+00:00", "2019-02-10T00:00:00+00:00", "2019-02-11T00:00:00+00:00", "2019-02-12T00:00:00+00:00", "2019-02-13T00:00:00+00:00", "2019-02-14T00:00:00+00:00", "2019-02-15T00:00:00+00:00", "2019-02-16T00:00:00+00:00", "2019-02-17T00:00:00+00:00", "2019-02-18T00:00:00+00:00", "2019-02-19T00:00:00+00:00", "2019-02-20T00:00:00+00:00", "2019-02-21T00:00:00+00:00", "2019-02-22T00:00:00+00:00", "2019-02-23T00:00:00+00:00", "2019-02-24T00:00:00+00:00", "2019-02-25T00:00:00+00:00", "2019-02-26T00:00:00+00:00", "2019-02-27T00:00:00+00:00", "2019-02-28T00:00:00+00:00", "2019-03-01T00:00:00+00:00", "2019-03-02T00:00:00+00:00", "2019-03-03T00:00:00+00:00", "2019-03-04T00:00:00+00:00", "2019-03-05T00:00:00+00:00", "2019-03-06T00:00:00+00:00", "2019-03-07T00:00:00+00:00", "2019-03-08T00:00:00+00:00", "2019-03-09T00:00:00+00:00", "2019-03-10T00:00:00+00:00", "2019-03-11T00:00:00+00:00", "2019-03-12T00:00:00+00:00", "2019-03-13T00:00:00+00:00", "2019-03-14T00:00:00+00:00", "2019-03-15T00:00:00+00:00", "2019-03-16T00:00:00+00:00", "2019-03-17T00:00:00+00:00", "2019-03-18T00:00:00+00:00", "2019-03-19T00:00:00+00:00", "2019-03-20T00:00:00+00:00", "2019-03-21T00:00:00+00:00", "2019-03-22T00:00:00+00:00", "2019-03-23T00:00:00+00:00", "2019-03-24T00:00:00+00:00", "2019-03-25T00:00:00+00:00", "2019-03-26T00:00:00+00:00", "2019-03-27T00:00:00+00:00", "2019-03-28T00:00:00+00:00", "2019-03-29T00:00:00+00:00", "2019-03-30T00:00:00+00:00", "2019-03-31T00:00:00+00:00", "2019-04-01T00:00:00+00:00", "2019-04-02T00:00:00+00:00", "2019-04-03T00:00:00+00:00", "2019-04-04T00:00:00+00:00", "2019-04-05T00:00:00+00:00", "2019-04-06T00:00:00+00:00", "2019-04-07T00:00:00+00:00", "2019-04-08T00:00:00+00:00", "2019-04-09T00:00:00+00:00", "2019-04-10T00:00:00+00:00", "2019-04-11T00:00:00+00:00", "2019-04-12T00:00:00+00:00", "2019-04-13T00:00:00+00:00", "2019-04-14T00:00:00+00:00", "2019-04-15T00:00:00+00:00", "2019-04-16T00:00:00+00:00", "2019-04-17T00:00:00+00:00", "2019-04-18T00:00:00+00:00", "2019-04-19T00:00:00+00:00", "2019-04-20T00:00:00+00:00", "2019-04-21T00:00:00+00:00", "2019-04-22T00:00:00+00:00", "2019-04-23T00:00:00+00:00", "2019-04-24T00:00:00+00:00", "2019-04-25T00:00:00+00:00", "2019-04-26T00:00:00+00:00", "2019-04-27T00:00:00+00:00", "2019-04-28T00:00:00+00:00", "2019-04-29T00:00:00+00:00", "2019-04-30T00:00:00+00:00", "2019-05-01T00:00:00+00:00", "2019-05-02T00:00:00+00:00", "2019-05-03T00:00:00+00:00", "2019-05-04T00:00:00+00:00", "2019-05-05T00:00:00+00:00", "2019-05-06T00:00:00+00:00", "2019-05-07T00:00:00+00:00", "2019-05-08T00:00:00+00:00", "2019-05-09T00:00:00+00:00", "2019-05-10T00:00:00+00:00", "2019-05-11T00:00:00+00:00", "2019-05-12T00:00:00+00:00", "2019-05-13T00:00:00+00:00", "2019-05-14T00:00:00+00:00", "2019-05-15T00:00:00+00:00", "2019-05-16T00:00:00+00:00", "2019-05-17T00:00:00+00:00", "2019-05-18T00:00:00+00:00", "2019-05-19T00:00:00+00:00", "2019-05-20T00:00:00+00:00", "2019-05-21T00:00:00+00:00", "2019-05-22T00:00:00+00:00", "2019-05-23T00:00:00+00:00", "2019-05-24T00:00:00+00:00", "2019-05-25T00:00:00+00:00", "2019-05-26T00:00:00+00:00", "2019-05-27T00:00:00+00:00", "2019-05-28T00:00:00+00:00", "2019-05-29T00:00:00+00:00", "2019-05-30T00:00:00+00:00", "2019-05-31T00:00:00+00:00", "2019-06-01T00:00:00+00:00", "2019-06-02T00:00:00+00:00", "2019-06-03T00:00:00+00:00", "2019-06-04T00:00:00+00:00", "2019-06-05T00:00:00+00:00", "2019-06-06T00:00:00+00:00", "2019-06-07T00:00:00+00:00", "2019-06-08T00:00:00+00:00", "2019-06-09T00:00:00+00:00", "2019-06-10T00:00:00+00:00", "2019-06-11T00:00:00+00:00", "2019-06-12T00:00:00+00:00", "2019-06-13T00:00:00+00:00", "2019-06-14T00:00:00+00:00", "2019-06-15T00:00:00+00:00", "2019-06-16T00:00:00+00:00", "2019-06-17T00:00:00+00:00", "2019-06-18T00:00:00+00:00", "2019-06-19T00:00:00+00:00", "2019-06-20T00:00:00+00:00", "2019-06-21T00:00:00+00:00", "2019-06-22T00:00:00+00:00", "2019-06-23T00:00:00+00:00", "2019-06-24T00:00:00+00:00", "2019-06-25T00:00:00+00:00", "2019-06-26T00:00:00+00:00", "2019-06-27T00:00:00+00:00", "2019-06-28T00:00:00+00:00", "2019-06-29T00:00:00+00:00", "2019-06-30T00:00:00+00:00", "2019-07-01T00:00:00+00:00", "2019-07-02T00:00:00+00:00", "2019-07-03T00:00:00+00:00", "2019-07-04T00:00:00+00:00", "2019-07-05T00:00:00+00:00", "2019-07-06T00:00:00+00:00", "2019-07-07T00:00:00+00:00", "2019-07-08T00:00:00+00:00", "2019-07-09T00:00:00+00:00", "2019-07-10T00:00:00+00:00", "2019-07-11T00:00:00+00:00", "2019-07-12T00:00:00+00:00", "2019-07-13T00:00:00+00:00", "2019-07-14T00:00:00+00:00", "2019-07-15T00:00:00+00:00", "2019-07-16T00:00:00+00:00", "2019-07-17T00:00:00+00:00", "2019-07-18T00:00:00+00:00", "2019-07-19T00:00:00+00:00", "2019-07-20T00:00:00+00:00", "2019-07-21T00:00:00+00:00", "2019-07-22T00:00:00+00:00", "2019-07-23T00:00:00+00:00", "2019-07-24T00:00:00+00:00", "2019-07-25T00:00:00+00:00", "2019-07-26T00:00:00+00:00", "2019-07-27T00:00:00+00:00", "2019-07-28T00:00:00+00:00", "2019-07-29T00:00:00+00:00", "2019-07-30T00:00:00+00:00", "2019-07-31T00:00:00+00:00", "2019-08-01T00:00:00+00:00", "2019-08-02T00:00:00+00:00", "2019-08-03T00:00:00+00:00", "2019-08-04T00:00:00+00:00", "2019-08-05T00:00:00+00:00", "2019-08-06T00:00:00+00:00", "2019-08-07T00:00:00+00:00", "2019-08-08T00:00:00+00:00", "2019-08-09T00:00:00+00:00", "2019-08-10T00:00:00+00:00", "2019-08-11T00:00:00+00:00", "2019-08-12T00:00:00+00:00", "2019-08-13T00:00:00+00:00", "2019-08-14T00:00:00+00:00", "2019-08-15T00:00:00+00:00", "2019-08-16T00:00:00+00:00", "2019-08-17T00:00:00+00:00", "2019-08-18T00:00:00+00:00", "2019-08-19T00:00:00+00:00", "2019-08-20T00:00:00+00:00", "2019-08-21T00:00:00+00:00", "2019-08-22T00:00:00+00:00", "2019-08-23T00:00:00+00:00", "2019-08-24T00:00:00+00:00", "2019-08-25T00:00:00+00:00", "2019-08-26T00:00:00+00:00", "2019-08-27T00:00:00+00:00", "2019-08-28T00:00:00+00:00", "2019-08-29T00:00:00+00:00", "2019-08-30T00:00:00+00:00", "2019-08-31T00:00:00+00:00", "2019-09-01T00:00:00+00:00", "2019-09-02T00:00:00+00:00", "2019-09-03T00:00:00+00:00", "2019-09-04T00:00:00+00:00", "2019-09-05T00:00:00+00:00", "2019-09-06T00:00:00+00:00", "2019-09-07T00:00:00+00:00", "2019-09-08T00:00:00+00:00", "2019-09-09T00:00:00+00:00", "2019-09-10T00:00:00+00:00", "2019-09-11T00:00:00+00:00", "2019-09-12T00:00:00+00:00", "2019-09-13T00:00:00+00:00", "2019-09-14T00:00:00+00:00", "2019-09-15T00:00:00+00:00", "2019-09-16T00:00:00+00:00", "2019-09-17T00:00:00+00:00", "2019-09-18T00:00:00+00:00", "2019-09-19T00:00:00+00:00", "2019-09-20T00:00:00+00:00", "2019-09-21T00:00:00+00:00", "2019-09-22T00:00:00+00:00", "2019-09-23T00:00:00+00:00", "2019-09-24T00:00:00+00:00", "2019-09-25T00:00:00+00:00", "2019-09-26T00:00:00+00:00", "2019-09-27T00:00:00+00:00", "2019-09-28T00:00:00+00:00", "2019-09-29T00:00:00+00:00", "2019-09-30T00:00:00+00:00", "2019-10-01T00:00:00+00:00", "2019-10-02T00:00:00+00:00", "2019-10-03T00:00:00+00:00", "2019-10-04T00:00:00+00:00", "2019-10-05T00:00:00+00:00", "2019-10-06T00:00:00+00:00", "2019-10-07T00:00:00+00:00", "2019-10-08T00:00:00+00:00", "2019-10-09T00:00:00+00:00", "2019-10-10T00:00:00+00:00", "2019-10-11T00:00:00+00:00", "2019-10-12T00:00:00+00:00", "2019-10-13T00:00:00+00:00", "2019-10-14T00:00:00+00:00", "2019-10-15T00:00:00+00:00", "2019-10-16T00:00:00+00:00", "2019-10-17T00:00:00+00:00", "2019-10-18T00:00:00+00:00", "2019-10-19T00:00:00+00:00", "2019-10-20T00:00:00+00:00", "2019-10-21T00:00:00+00:00", "2019-10-22T00:00:00+00:00", "2019-10-23T00:00:00+00:00", "2019-10-24T00:00:00+00:00", "2019-10-25T00:00:00+00:00", "2019-10-26T00:00:00+00:00", "2019-10-27T00:00:00+00:00", "2019-10-28T00:00:00+00:00", "2019-10-29T00:00:00+00:00", "2019-10-30T00:00:00+00:00", "2019-10-31T00:00:00+00:00", "2019-11-01T00:00:00+00:00", "2019-11-02T00:00:00+00:00", "2019-11-03T00:00:00+00:00", "2019-11-04T00:00:00+00:00", "2019-11-05T00:00:00+00:00", "2019-11-06T00:00:00+00:00", "2019-11-07T00:00:00+00:00", "2019-11-08T00:00:00+00:00", "2019-11-09T00:00:00+00:00", "2019-11-10T00:00:00+00:00", "2019-11-11T00:00:00+00:00", "2019-11-12T00:00:00+00:00", "2019-11-13T00:00:00+00:00", "2019-11-14T00:00:00+00:00", "2019-11-15T00:00:00+00:00", "2019-11-16T00:00:00+00:00", "2019-11-17T00:00:00+00:00", "2019-11-18T00:00:00+00:00", "2019-11-19T00:00:00+00:00", "2019-11-20T00:00:00+00:00", "2019-11-21T00:00:00+00:00", "2019-11-22T00:00:00+00:00", "2019-11-23T00:00:00+00:00", "2019-11-24T00:00:00+00:00", "2019-11-25T00:00:00+00:00", "2019-11-26T00:00:00+00:00", "2019-11-27T00:00:00+00:00", "2019-11-28T00:00:00+00:00", "2019-11-29T00:00:00+00:00", "2019-11-30T00:00:00+00:00", "2019-12-01T00:00:00+00:00", "2019-12-02T00:00:00+00:00", "2019-12-03T00:00:00+00:00", "2019-12-04T00:00:00+00:00", "2019-12-05T00:00:00+00:00", "2019-12-06T00:00:00+00:00", "2019-12-07T00:00:00+00:00", "2019-12-08T00:00:00+00:00", "2019-12-09T00:00:00+00:00", "2019-12-10T00:00:00+00:00", "2019-12-11T00:00:00+00:00", "2019-12-12T00:00:00+00:00", "2019-12-13T00:00:00+00:00", "2019-12-14T00:00:00+00:00", "2019-12-15T00:00:00+00:00", "2019-12-16T00:00:00+00:00", "2019-12-17T00:00:00+00:00", "2019-12-18T00:00:00+00:00", "2019-12-19T00:00:00+00:00", "2019-12-20T00:00:00+00:00", "2019-12-21T00:00:00+00:00", "2019-12-22T00:00:00+00:00", "2019-12-23T00:00:00+00:00", "2019-12-24T00:00:00+00:00", "2019-12-25T00:00:00+00:00", "2019-12-26T00:00:00+00:00", "2019-12-27T00:00:00+00:00", "2019-12-28T00:00:00+00:00", "2019-12-29T00:00:00+00:00", "2019-12-30T00:00:00+00:00", "2019-12-31T00:00:00+00:00", "2020-01-01T00:00:00+00:00", "2020-01-02T00:00:00+00:00", "2020-01-03T00:00:00+00:00", "2020-01-04T00:00:00+00:00", "2020-01-05T00:00:00+00:00", "2020-01-06T00:00:00+00:00", "2020-01-07T00:00:00+00:00", "2020-01-08T00:00:00+00:00", "2020-01-09T00:00:00+00:00", "2020-01-10T00:00:00+00:00", "2020-01-11T00:00:00+00:00", "2020-01-12T00:00:00+00:00", "2020-01-13T00:00:00+00:00", "2020-01-14T00:00:00+00:00", "2020-01-15T00:00:00+00:00", "2020-01-16T00:00:00+00:00", "2020-01-17T00:00:00+00:00", "2020-01-18T00:00:00+00:00", "2020-01-19T00:00:00+00:00", "2020-01-20T00:00:00+00:00", "2020-01-21T00:00:00+00:00", "2020-01-22T00:00:00+00:00", "2020-01-23T00:00:00+00:00", "2020-01-24T00:00:00+00:00", "2020-01-25T00:00:00+00:00", "2020-01-26T00:00:00+00:00", "2020-01-27T00:00:00+00:00", "2020-01-28T00:00:00+00:00", "2020-01-29T00:00:00+00:00", "2020-01-30T00:00:00+00:00", "2020-01-31T00:00:00+00:00", "2020-02-01T00:00:00+00:00", "2020-02-02T00:00:00+00:00", "2020-02-03T00:00:00+00:00", "2020-02-04T00:00:00+00:00", "2020-02-05T00:00:00+00:00", "2020-02-06T00:00:00+00:00", "2020-02-07T00:00:00+00:00", "2020-02-08T00:00:00+00:00", "2020-02-09T00:00:00+00:00", "2020-02-10T00:00:00+00:00", "2020-02-11T00:00:00+00:00", "2020-02-12T00:00:00+00:00", "2020-02-13T00:00:00+00:00", "2020-02-14T00:00:00+00:00", "2020-02-15T00:00:00+00:00", "2020-02-16T00:00:00+00:00", "2020-02-17T00:00:00+00:00", "2020-02-18T00:00:00+00:00", "2020-02-19T00:00:00+00:00", "2020-02-20T00:00:00+00:00", "2020-02-21T00:00:00+00:00", "2020-02-22T00:00:00+00:00", "2020-02-23T00:00:00+00:00", "2020-02-24T00:00:00+00:00", "2020-02-25T00:00:00+00:00", "2020-02-26T00:00:00+00:00", "2020-02-27T00:00:00+00:00", "2020-02-28T00:00:00+00:00", "2020-02-29T00:00:00+00:00", "2020-03-01T00:00:00+00:00", "2020-03-02T00:00:00+00:00", "2020-03-03T00:00:00+00:00", "2020-03-04T00:00:00+00:00", "2020-03-05T00:00:00+00:00", "2020-03-06T00:00:00+00:00", "2020-03-07T00:00:00+00:00", "2020-03-08T00:00:00+00:00", "2020-03-09T00:00:00+00:00", "2020-03-10T00:00:00+00:00", "2020-03-11T00:00:00+00:00", "2020-03-12T00:00:00+00:00", "2020-03-13T00:00:00+00:00", "2020-03-14T00:00:00+00:00", "2020-03-15T00:00:00+00:00", "2020-03-16T00:00:00+00:00", "2020-03-17T00:00:00+00:00", "2020-03-18T00:00:00+00:00", "2020-03-19T00:00:00+00:00", "2020-03-20T00:00:00+00:00", "2020-03-21T00:00:00+00:00", "2020-03-22T00:00:00+00:00", "2020-03-23T00:00:00+00:00", "2020-03-24T00:00:00+00:00", "2020-03-25T00:00:00+00:00", "2020-03-26T00:00:00+00:00", "2020-03-27T00:00:00+00:00", "2020-03-28T00:00:00+00:00", "2020-03-29T00:00:00+00:00", "2020-03-30T00:00:00+00:00", "2020-03-31T00:00:00+00:00", "2020-04-01T00:00:00+00:00", "2020-04-02T00:00:00+00:00", "2020-04-03T00:00:00+00:00", "2020-04-04T00:00:00+00:00", "2020-04-05T00:00:00+00:00", "2020-04-06T00:00:00+00:00", "2020-04-07T00:00:00+00:00", "2020-04-08T00:00:00+00:00", "2020-04-09T00:00:00+00:00", "2020-04-10T00:00:00+00:00", "2020-04-11T00:00:00+00:00", "2020-04-12T00:00:00+00:00", "2020-04-13T00:00:00+00:00", "2020-04-14T00:00:00+00:00", "2020-04-15T00:00:00+00:00", "2020-04-16T00:00:00+00:00", "2020-04-17T00:00:00+00:00", "2020-04-18T00:00:00+00:00", "2020-04-19T00:00:00+00:00", "2020-04-20T00:00:00+00:00", "2020-04-21T00:00:00+00:00", "2020-04-22T00:00:00+00:00", "2020-04-23T00:00:00+00:00", "2020-04-24T00:00:00+00:00", "2020-04-25T00:00:00+00:00", "2020-04-26T00:00:00+00:00", "2020-04-27T00:00:00+00:00", "2020-04-28T00:00:00+00:00", "2020-04-29T00:00:00+00:00", "2020-04-30T00:00:00+00:00", "2020-05-01T00:00:00+00:00", "2020-05-02T00:00:00+00:00", "2020-05-03T00:00:00+00:00", "2020-05-04T00:00:00+00:00", "2020-05-05T00:00:00+00:00", "2020-05-06T00:00:00+00:00", "2020-05-07T00:00:00+00:00", "2020-05-08T00:00:00+00:00", "2020-05-09T00:00:00+00:00", "2020-05-10T00:00:00+00:00", "2020-05-11T00:00:00+00:00", "2020-05-12T00:00:00+00:00", "2020-05-13T00:00:00+00:00", "2020-05-14T00:00:00+00:00", "2020-05-15T00:00:00+00:00", "2020-05-16T00:00:00+00:00", "2020-05-17T00:00:00+00:00", "2020-05-18T00:00:00+00:00", "2020-05-19T00:00:00+00:00", "2020-05-20T00:00:00+00:00", "2020-05-21T00:00:00+00:00", "2020-05-22T00:00:00+00:00", "2020-05-23T00:00:00+00:00", "2020-05-24T00:00:00+00:00", "2020-05-25T00:00:00+00:00", "2020-05-26T00:00:00+00:00", "2020-05-27T00:00:00+00:00", "2020-05-28T00:00:00+00:00", "2020-05-29T00:00:00+00:00", "2020-05-30T00:00:00+00:00", "2020-05-31T00:00:00+00:00", "2020-06-01T00:00:00+00:00", "2020-06-02T00:00:00+00:00", "2020-06-03T00:00:00+00:00", "2020-06-04T00:00:00+00:00", "2020-06-05T00:00:00+00:00", "2020-06-06T00:00:00+00:00", "2020-06-07T00:00:00+00:00", "2020-06-08T00:00:00+00:00", "2020-06-09T00:00:00+00:00", "2020-06-10T00:00:00+00:00", "2020-06-11T00:00:00+00:00", "2020-06-12T00:00:00+00:00", "2020-06-13T00:00:00+00:00", "2020-06-14T00:00:00+00:00", "2020-06-15T00:00:00+00:00", "2020-06-16T00:00:00+00:00", "2020-06-17T00:00:00+00:00", "2020-06-18T00:00:00+00:00", "2020-06-19T00:00:00+00:00", "2020-06-20T00:00:00+00:00", "2020-06-21T00:00:00+00:00", "2020-06-22T00:00:00+00:00", "2020-06-23T00:00:00+00:00", "2020-06-24T00:00:00+00:00", "2020-06-25T00:00:00+00:00", "2020-06-26T00:00:00+00:00", "2020-06-27T00:00:00+00:00", "2020-06-28T00:00:00+00:00", "2020-06-29T00:00:00+00:00", "2020-06-30T00:00:00+00:00", "2020-07-01T00:00:00+00:00", "2020-07-02T00:00:00+00:00", "2020-07-03T00:00:00+00:00", "2020-07-04T00:00:00+00:00", "2020-07-05T00:00:00+00:00", "2020-07-06T00:00:00+00:00", "2020-07-07T00:00:00+00:00", "2020-07-08T00:00:00+00:00", "2020-07-09T00:00:00+00:00", "2020-07-10T00:00:00+00:00", "2020-07-11T00:00:00+00:00", "2020-07-12T00:00:00+00:00", "2020-07-13T00:00:00+00:00", "2020-07-14T00:00:00+00:00", "2020-07-15T00:00:00+00:00", "2020-07-16T00:00:00+00:00", "2020-07-17T00:00:00+00:00", "2020-07-18T00:00:00+00:00", "2020-07-19T00:00:00+00:00", "2020-07-20T00:00:00+00:00", "2020-07-21T00:00:00+00:00", "2020-07-22T00:00:00+00:00", "2020-07-23T00:00:00+00:00", "2020-07-24T00:00:00+00:00", "2020-07-25T00:00:00+00:00", "2020-07-26T00:00:00+00:00", "2020-07-27T00:00:00+00:00", "2020-07-28T00:00:00+00:00", "2020-07-29T00:00:00+00:00", "2020-07-30T00:00:00+00:00", "2020-07-31T00:00:00+00:00", "2020-08-01T00:00:00+00:00", "2020-08-02T00:00:00+00:00", "2020-08-03T00:00:00+00:00", "2020-08-04T00:00:00+00:00", "2020-08-05T00:00:00+00:00", "2020-08-06T00:00:00+00:00", "2020-08-07T00:00:00+00:00", "2020-08-08T00:00:00+00:00", "2020-08-09T00:00:00+00:00", "2020-08-10T00:00:00+00:00", "2020-08-11T00:00:00+00:00", "2020-08-12T00:00:00+00:00", "2020-08-13T00:00:00+00:00", "2020-08-14T00:00:00+00:00", "2020-08-15T00:00:00+00:00", "2020-08-16T00:00:00+00:00", "2020-08-17T00:00:00+00:00", "2020-08-18T00:00:00+00:00", "2020-08-19T00:00:00+00:00", "2020-08-20T00:00:00+00:00", "2020-08-21T00:00:00+00:00", "2020-08-22T00:00:00+00:00", "2020-08-23T00:00:00+00:00", "2020-08-24T00:00:00+00:00", "2020-08-25T00:00:00+00:00", "2020-08-26T00:00:00+00:00", "2020-08-27T00:00:00+00:00", "2020-08-28T00:00:00+00:00", "2020-08-29T00:00:00+00:00", "2020-08-30T00:00:00+00:00", "2020-08-31T00:00:00+00:00", "2020-09-01T00:00:00+00:00", "2020-09-02T00:00:00+00:00", "2020-09-03T00:00:00+00:00", "2020-09-04T00:00:00+00:00", "2020-09-05T00:00:00+00:00", "2020-09-06T00:00:00+00:00", "2020-09-07T00:00:00+00:00", "2020-09-08T00:00:00+00:00", "2020-09-09T00:00:00+00:00", "2020-09-10T00:00:00+00:00", "2020-09-11T00:00:00+00:00", "2020-09-12T00:00:00+00:00", "2020-09-13T00:00:00+00:00", "2020-09-14T00:00:00+00:00", "2020-09-15T00:00:00+00:00", "2020-09-16T00:00:00+00:00", "2020-09-17T00:00:00+00:00", "2020-09-18T00:00:00+00:00", "2020-09-19T00:00:00+00:00", "2020-09-20T00:00:00+00:00", "2020-09-21T00:00:00+00:00", "2020-09-22T00:00:00+00:00", "2020-09-23T00:00:00+00:00", "2020-09-24T00:00:00+00:00", "2020-09-25T00:00:00+00:00", "2020-09-26T00:00:00+00:00", "2020-09-27T00:00:00+00:00", "2020-09-28T00:00:00+00:00", "2020-09-29T00:00:00+00:00", "2020-09-30T00:00:00+00:00", "2020-10-01T00:00:00+00:00", "2020-10-02T00:00:00+00:00", "2020-10-03T00:00:00+00:00", "2020-10-04T00:00:00+00:00", "2020-10-05T00:00:00+00:00", "2020-10-06T00:00:00+00:00", "2020-10-07T00:00:00+00:00", "2020-10-08T00:00:00+00:00", "2020-10-09T00:00:00+00:00", "2020-10-10T00:00:00+00:00", "2020-10-11T00:00:00+00:00", "2020-10-12T00:00:00+00:00", "2020-10-13T00:00:00+00:00", "2020-10-14T00:00:00+00:00", "2020-10-15T00:00:00+00:00", "2020-10-16T00:00:00+00:00", "2020-10-17T00:00:00+00:00", "2020-10-18T00:00:00+00:00", "2020-10-19T00:00:00+00:00", "2020-10-20T00:00:00+00:00", "2020-10-21T00:00:00+00:00", "2020-10-22T00:00:00+00:00", "2020-10-23T00:00:00+00:00", "2020-10-24T00:00:00+00:00", "2020-10-25T00:00:00+00:00", "2020-10-26T00:00:00+00:00", "2020-10-27T00:00:00+00:00", "2020-10-28T00:00:00+00:00", "2020-10-29T00:00:00+00:00", "2020-10-30T00:00:00+00:00", "2020-10-31T00:00:00+00:00", "2020-11-01T00:00:00+00:00", "2020-11-02T00:00:00+00:00", "2020-11-03T00:00:00+00:00", "2020-11-04T00:00:00+00:00", "2020-11-05T00:00:00+00:00", "2020-11-06T00:00:00+00:00", "2020-11-07T00:00:00+00:00", "2020-11-08T00:00:00+00:00", "2020-11-09T00:00:00+00:00", "2020-11-10T00:00:00+00:00", "2020-11-11T00:00:00+00:00", "2020-11-12T00:00:00+00:00", "2020-11-13T00:00:00+00:00", "2020-11-14T00:00:00+00:00", "2020-11-15T00:00:00+00:00", "2020-11-16T00:00:00+00:00", "2020-11-17T00:00:00+00:00", "2020-11-18T00:00:00+00:00", "2020-11-19T00:00:00+00:00", "2020-11-20T00:00:00+00:00", "2020-11-21T00:00:00+00:00", "2020-11-22T00:00:00+00:00", "2020-11-23T00:00:00+00:00", "2020-11-24T00:00:00+00:00", "2020-11-25T00:00:00+00:00", "2020-11-26T00:00:00+00:00", "2020-11-27T00:00:00+00:00", "2020-11-28T00:00:00+00:00", "2020-11-29T00:00:00+00:00", "2020-11-30T00:00:00+00:00", "2020-12-01T00:00:00+00:00", "2020-12-02T00:00:00+00:00", "2020-12-03T00:00:00+00:00", "2020-12-04T00:00:00+00:00", "2020-12-05T00:00:00+00:00", "2020-12-06T00:00:00+00:00", "2020-12-07T00:00:00+00:00", "2020-12-08T00:00:00+00:00", "2020-12-09T00:00:00+00:00", "2020-12-10T00:00:00+00:00", "2020-12-11T00:00:00+00:00", "2020-12-12T00:00:00+00:00", "2020-12-13T00:00:00+00:00", "2020-12-14T00:00:00+00:00", "2020-12-15T00:00:00+00:00", "2020-12-16T00:00:00+00:00", "2020-12-17T00:00:00+00:00", "2020-12-18T00:00:00+00:00", "2020-12-19T00:00:00+00:00", "2020-12-20T00:00:00+00:00", "2020-12-21T00:00:00+00:00", "2020-12-22T00:00:00+00:00", "2020-12-23T00:00:00+00:00", "2020-12-24T00:00:00+00:00", "2020-12-25T00:00:00+00:00", "2020-12-26T00:00:00+00:00", "2020-12-27T00:00:00+00:00", "2020-12-28T00:00:00+00:00", "2020-12-29T00:00:00+00:00", "2020-12-30T00:00:00+00:00", "2020-12-31T00:00:00+00:00", "2021-01-01T00:00:00+00:00", "2021-01-02T00:00:00+00:00", "2021-01-03T00:00:00+00:00", "2021-01-04T00:00:00+00:00", "2021-01-05T00:00:00+00:00", "2021-01-06T00:00:00+00:00", "2021-01-07T00:00:00+00:00", "2021-01-08T00:00:00+00:00", "2021-01-09T00:00:00+00:00", "2021-01-10T00:00:00+00:00", "2021-01-11T00:00:00+00:00", "2021-01-12T00:00:00+00:00", "2021-01-13T00:00:00+00:00", "2021-01-14T00:00:00+00:00", "2021-01-15T00:00:00+00:00", "2021-01-16T00:00:00+00:00", "2021-01-17T00:00:00+00:00", "2021-01-18T00:00:00+00:00", "2021-01-19T00:00:00+00:00", "2021-01-20T00:00:00+00:00", "2021-01-21T00:00:00+00:00", "2021-01-22T00:00:00+00:00", "2021-01-23T00:00:00+00:00", "2021-01-24T00:00:00+00:00", "2021-01-25T00:00:00+00:00", "2021-01-26T00:00:00+00:00", "2021-01-27T00:00:00+00:00", "2021-01-28T00:00:00+00:00", "2021-01-29T00:00:00+00:00", "2021-01-30T00:00:00+00:00", "2021-01-31T00:00:00+00:00", "2021-02-01T00:00:00+00:00", "2021-02-02T00:00:00+00:00", "2021-02-03T00:00:00+00:00", "2021-02-04T00:00:00+00:00", "2021-02-05T00:00:00+00:00", "2021-02-06T00:00:00+00:00", "2021-02-07T00:00:00+00:00", "2021-02-08T00:00:00+00:00", "2021-02-09T00:00:00+00:00", "2021-02-10T00:00:00+00:00", "2021-02-11T00:00:00+00:00", "2021-02-12T00:00:00+00:00", "2021-02-13T00:00:00+00:00", "2021-02-14T00:00:00+00:00", "2021-02-15T00:00:00+00:00", "2021-02-16T00:00:00+00:00", "2021-02-17T00:00:00+00:00", "2021-02-18T00:00:00+00:00", "2021-02-19T00:00:00+00:00", "2021-02-20T00:00:00+00:00", "2021-02-21T00:00:00+00:00", "2021-02-22T00:00:00+00:00", "2021-02-23T00:00:00+00:00", "2021-02-24T00:00:00+00:00", "2021-02-25T00:00:00+00:00", "2021-02-26T00:00:00+00:00", "2021-02-27T00:00:00+00:00", "2021-02-28T00:00:00+00:00", "2021-03-01T00:00:00+00:00", "2021-03-02T00:00:00+00:00", "2021-03-03T00:00:00+00:00", "2021-03-04T00:00:00+00:00", "2021-03-05T00:00:00+00:00", "2021-03-06T00:00:00+00:00", "2021-03-07T00:00:00+00:00", "2021-03-08T00:00:00+00:00", "2021-03-09T00:00:00+00:00", "2021-03-10T00:00:00+00:00", "2021-03-11T00:00:00+00:00", "2021-03-12T00:00:00+00:00", "2021-03-13T00:00:00+00:00", "2021-03-14T00:00:00+00:00", "2021-03-15T00:00:00+00:00", "2021-03-16T00:00:00+00:00", "2021-03-17T00:00:00+00:00", "2021-03-18T00:00:00+00:00", "2021-03-19T00:00:00+00:00", "2021-03-20T00:00:00+00:00", "2021-03-21T00:00:00+00:00", "2021-03-22T00:00:00+00:00", "2021-03-23T00:00:00+00:00", "2021-03-24T00:00:00+00:00", "2021-03-25T00:00:00+00:00", "2021-03-26T00:00:00+00:00", "2021-03-27T00:00:00+00:00", "2021-03-28T00:00:00+00:00", "2021-03-29T00:00:00+00:00", "2021-03-30T00:00:00+00:00", "2021-03-31T00:00:00+00:00", "2021-04-01T00:00:00+00:00", "2021-04-02T00:00:00+00:00", "2021-04-03T00:00:00+00:00", "2021-04-04T00:00:00+00:00", "2021-04-05T00:00:00+00:00", "2021-04-06T00:00:00+00:00", "2021-04-07T00:00:00+00:00", "2021-04-08T00:00:00+00:00", "2021-04-09T00:00:00+00:00", "2021-04-10T00:00:00+00:00", "2021-04-11T00:00:00+00:00", "2021-04-12T00:00:00+00:00", "2021-04-13T00:00:00+00:00", "2021-04-14T00:00:00+00:00", "2021-04-15T00:00:00+00:00", "2021-04-16T00:00:00+00:00", "2021-04-17T00:00:00+00:00", "2021-04-18T00:00:00+00:00", "2021-04-19T00:00:00+00:00", "2021-04-20T00:00:00+00:00", "2021-04-21T00:00:00+00:00", "2021-04-22T00:00:00+00:00", "2021-04-23T00:00:00+00:00", "2021-04-24T00:00:00+00:00", "2021-04-25T00:00:00+00:00", "2021-04-26T00:00:00+00:00", "2021-04-27T00:00:00+00:00", "2021-04-28T00:00:00+00:00", "2021-04-29T00:00:00+00:00", "2021-04-30T00:00:00+00:00", "2021-05-01T00:00:00+00:00", "2021-05-02T00:00:00+00:00", "2021-05-03T00:00:00+00:00", "2021-05-04T00:00:00+00:00", "2021-05-05T00:00:00+00:00", "2021-05-06T00:00:00+00:00", "2021-05-07T00:00:00+00:00", "2021-05-08T00:00:00+00:00", "2021-05-09T00:00:00+00:00", "2021-05-10T00:00:00+00:00", "2021-05-11T00:00:00+00:00", "2021-05-12T00:00:00+00:00", "2021-05-13T00:00:00+00:00", "2021-05-14T00:00:00+00:00", "2021-05-15T00:00:00+00:00", "2021-05-16T00:00:00+00:00", "2021-05-17T00:00:00+00:00", "2021-05-18T00:00:00+00:00", "2021-05-19T00:00:00+00:00", "2021-05-20T00:00:00+00:00", "2021-05-21T00:00:00+00:00", "2021-05-22T00:00:00+00:00", "2021-05-23T00:00:00+00:00", "2021-05-24T00:00:00+00:00", "2021-05-25T00:00:00+00:00", "2021-05-26T00:00:00+00:00", "2021-05-27T00:00:00+00:00", "2021-05-28T00:00:00+00:00", "2021-05-29T00:00:00+00:00", "2021-05-30T00:00:00+00:00", "2021-05-31T00:00:00+00:00", "2021-06-01T00:00:00+00:00", "2021-06-02T00:00:00+00:00", "2021-06-03T00:00:00+00:00", "2021-06-04T00:00:00+00:00", "2021-06-05T00:00:00+00:00", "2021-06-06T00:00:00+00:00", "2021-06-07T00:00:00+00:00", "2021-06-08T00:00:00+00:00", "2021-06-09T00:00:00+00:00", "2021-06-10T00:00:00+00:00", "2021-06-11T00:00:00+00:00", "2021-06-12T00:00:00+00:00", "2021-06-13T00:00:00+00:00", "2021-06-14T00:00:00+00:00", "2021-06-15T00:00:00+00:00", "2021-06-16T00:00:00+00:00", "2021-06-17T00:00:00+00:00", "2021-06-18T00:00:00+00:00", "2021-06-19T00:00:00+00:00", "2021-06-20T00:00:00+00:00", "2021-06-21T00:00:00+00:00", "2021-06-22T00:00:00+00:00", "2021-06-23T00:00:00+00:00", "2021-06-24T00:00:00+00:00", "2021-06-25T00:00:00+00:00", "2021-06-26T00:00:00+00:00", "2021-06-27T00:00:00+00:00", "2021-06-28T00:00:00+00:00", "2021-06-29T00:00:00+00:00", "2021-06-30T00:00:00+00:00", "2021-07-01T00:00:00+00:00", "2021-07-02T00:00:00+00:00", "2021-07-03T00:00:00+00:00", "2021-07-04T00:00:00+00:00", "2021-07-05T00:00:00+00:00", "2021-07-06T00:00:00+00:00", "2021-07-07T00:00:00+00:00", "2021-07-08T00:00:00+00:00", "2021-07-09T00:00:00+00:00", "2021-07-10T00:00:00+00:00", "2021-07-11T00:00:00+00:00", "2021-07-12T00:00:00+00:00", "2021-07-13T00:00:00+00:00", "2021-07-14T00:00:00+00:00", "2021-07-15T00:00:00+00:00", "2021-07-16T00:00:00+00:00", "2021-07-17T00:00:00+00:00", "2021-07-18T00:00:00+00:00", "2021-07-19T00:00:00+00:00", "2021-07-20T00:00:00+00:00", "2021-07-21T00:00:00+00:00", "2021-07-22T00:00:00+00:00", "2021-07-23T00:00:00+00:00", "2021-07-24T00:00:00+00:00", "2021-07-25T00:00:00+00:00", "2021-07-26T00:00:00+00:00", "2021-07-27T00:00:00+00:00", "2021-07-28T00:00:00+00:00", "2021-07-29T00:00:00+00:00", "2021-07-30T00:00:00+00:00", "2021-07-31T00:00:00+00:00", "2021-08-01T00:00:00+00:00", "2021-08-02T00:00:00+00:00", "2021-08-03T00:00:00+00:00", "2021-08-04T00:00:00+00:00", "2021-08-05T00:00:00+00:00", "2021-08-06T00:00:00+00:00", "2021-08-07T00:00:00+00:00", "2021-08-08T00:00:00+00:00", "2021-08-09T00:00:00+00:00", "2021-08-10T00:00:00+00:00", "2021-08-11T00:00:00+00:00", "2021-08-12T00:00:00+00:00", "2021-08-13T00:00:00+00:00", "2021-08-14T00:00:00+00:00", "2021-08-15T00:00:00+00:00", "2021-08-16T00:00:00+00:00", "2021-08-17T00:00:00+00:00", "2021-08-18T00:00:00+00:00", "2021-08-19T00:00:00+00:00", "2021-08-20T00:00:00+00:00", "2021-08-21T00:00:00+00:00", "2021-08-22T00:00:00+00:00", "2021-08-23T00:00:00+00:00", "2021-08-24T00:00:00+00:00", "2021-08-25T00:00:00+00:00", "2021-08-26T00:00:00+00:00", "2021-08-27T00:00:00+00:00", "2021-08-28T00:00:00+00:00", "2021-08-29T00:00:00+00:00", "2021-08-30T00:00:00+00:00", "2021-08-31T00:00:00+00:00", "2021-09-01T00:00:00+00:00", "2021-09-02T00:00:00+00:00", "2021-09-03T00:00:00+00:00", "2021-09-04T00:00:00+00:00", "2021-09-05T00:00:00+00:00", "2021-09-06T00:00:00+00:00", "2021-09-07T00:00:00+00:00", "2021-09-08T00:00:00+00:00", "2021-09-09T00:00:00+00:00", "2021-09-10T00:00:00+00:00", "2021-09-11T00:00:00+00:00", "2021-09-12T00:00:00+00:00", "2021-09-13T00:00:00+00:00", "2021-09-14T00:00:00+00:00", "2021-09-15T00:00:00+00:00", "2021-09-16T00:00:00+00:00", "2021-09-17T00:00:00+00:00", "2021-09-18T00:00:00+00:00", "2021-09-19T00:00:00+00:00", "2021-09-20T00:00:00+00:00", "2021-09-21T00:00:00+00:00", "2021-09-22T00:00:00+00:00", "2021-09-23T00:00:00+00:00", "2021-09-24T00:00:00+00:00", "2021-09-25T00:00:00+00:00", "2021-09-26T00:00:00+00:00", "2021-09-27T00:00:00+00:00", "2021-09-28T00:00:00+00:00", "2021-09-29T00:00:00+00:00", "2021-09-30T00:00:00+00:00", "2021-10-01T00:00:00+00:00", "2021-10-02T00:00:00+00:00", "2021-10-03T00:00:00+00:00", "2021-10-04T00:00:00+00:00", "2021-10-05T00:00:00+00:00", "2021-10-06T00:00:00+00:00", "2021-10-07T00:00:00+00:00", "2021-10-08T00:00:00+00:00", "2021-10-09T00:00:00+00:00", "2021-10-10T00:00:00+00:00", "2021-10-11T00:00:00+00:00", "2021-10-12T00:00:00+00:00", "2021-10-13T00:00:00+00:00", "2021-10-14T00:00:00+00:00", "2021-10-15T00:00:00+00:00", "2021-10-16T00:00:00+00:00", "2021-10-17T00:00:00+00:00", "2021-10-18T00:00:00+00:00", "2021-10-19T00:00:00+00:00", "2021-10-20T00:00:00+00:00", "2021-10-21T00:00:00+00:00", "2021-10-22T00:00:00+00:00", "2021-10-23T00:00:00+00:00", "2021-10-24T00:00:00+00:00", "2021-10-25T00:00:00+00:00", "2021-10-26T00:00:00+00:00", "2021-10-27T00:00:00+00:00", "2021-10-28T00:00:00+00:00", "2021-10-29T00:00:00+00:00", "2021-10-30T00:00:00+00:00", "2021-10-31T00:00:00+00:00", "2021-11-01T00:00:00+00:00", "2021-11-02T00:00:00+00:00", "2021-11-03T00:00:00+00:00", "2021-11-04T00:00:00+00:00", "2021-11-05T00:00:00+00:00", "2021-11-06T00:00:00+00:00", "2021-11-07T00:00:00+00:00", "2021-11-08T00:00:00+00:00", "2021-11-09T00:00:00+00:00", "2021-11-10T00:00:00+00:00", "2021-11-11T00:00:00+00:00", "2021-11-12T00:00:00+00:00", "2021-11-13T00:00:00+00:00", "2021-11-14T00:00:00+00:00", "2021-11-15T00:00:00+00:00", "2021-11-16T00:00:00+00:00", "2021-11-17T00:00:00+00:00", "2021-11-18T00:00:00+00:00", "2021-11-19T00:00:00+00:00", "2021-11-20T00:00:00+00:00", "2021-11-21T00:00:00+00:00", "2021-11-22T00:00:00+00:00", "2021-11-23T00:00:00+00:00", "2021-11-24T00:00:00+00:00", "2021-11-25T00:00:00+00:00", "2021-11-26T00:00:00+00:00", "2021-11-27T00:00:00+00:00", "2021-11-28T00:00:00+00:00", "2021-11-29T00:00:00+00:00", "2021-11-30T00:00:00+00:00", "2021-12-01T00:00:00+00:00", "2021-12-02T00:00:00+00:00", "2021-12-03T00:00:00+00:00", "2021-12-04T00:00:00+00:00", "2021-12-05T00:00:00+00:00", "2021-12-06T00:00:00+00:00", "2021-12-07T00:00:00+00:00", "2021-12-08T00:00:00+00:00", "2021-12-09T00:00:00+00:00", "2021-12-10T00:00:00+00:00", "2021-12-11T00:00:00+00:00", "2021-12-12T00:00:00+00:00", "2021-12-13T00:00:00+00:00", "2021-12-14T00:00:00+00:00", "2021-12-15T00:00:00+00:00", "2021-12-16T00:00:00+00:00", "2021-12-17T00:00:00+00:00", "2021-12-18T00:00:00+00:00", "2021-12-19T00:00:00+00:00", "2021-12-20T00:00:00+00:00", "2021-12-21T00:00:00+00:00", "2021-12-22T00:00:00+00:00", "2021-12-23T00:00:00+00:00", "2021-12-24T00:00:00+00:00", "2021-12-25T00:00:00+00:00", "2021-12-26T00:00:00+00:00", "2021-12-27T00:00:00+00:00", "2021-12-28T00:00:00+00:00", "2021-12-29T00:00:00+00:00", "2021-12-30T00:00:00+00:00", "2021-12-31T00:00:00+00:00", "2022-01-01T00:00:00+00:00", "2022-01-02T00:00:00+00:00", "2022-01-03T00:00:00+00:00", "2022-01-04T00:00:00+00:00", "2022-01-05T00:00:00+00:00", "2022-01-06T00:00:00+00:00", "2022-01-07T00:00:00+00:00", "2022-01-08T00:00:00+00:00", "2022-01-09T00:00:00+00:00", "2022-01-10T00:00:00+00:00", "2022-01-11T00:00:00+00:00", "2022-01-12T00:00:00+00:00", "2022-01-13T00:00:00+00:00", "2022-01-14T00:00:00+00:00", "2022-01-15T00:00:00+00:00", "2022-01-16T00:00:00+00:00", "2022-01-17T00:00:00+00:00", "2022-01-18T00:00:00+00:00", "2022-01-19T00:00:00+00:00", "2022-01-20T00:00:00+00:00", "2022-01-21T00:00:00+00:00", "2022-01-22T00:00:00+00:00", "2022-01-23T00:00:00+00:00", "2022-01-24T00:00:00+00:00", "2022-01-25T00:00:00+00:00", "2022-01-26T00:00:00+00:00", "2022-01-27T00:00:00+00:00", "2022-01-28T00:00:00+00:00", "2022-01-29T00:00:00+00:00", "2022-01-30T00:00:00+00:00", "2022-01-31T00:00:00+00:00", "2022-02-01T00:00:00+00:00", "2022-02-02T00:00:00+00:00", "2022-02-03T00:00:00+00:00", "2022-02-04T00:00:00+00:00", "2022-02-05T00:00:00+00:00", "2022-02-06T00:00:00+00:00", "2022-02-07T00:00:00+00:00", "2022-02-08T00:00:00+00:00", "2022-02-09T00:00:00+00:00", "2022-02-10T00:00:00+00:00", "2022-02-11T00:00:00+00:00", "2022-02-12T00:00:00+00:00", "2022-02-13T00:00:00+00:00", "2022-02-14T00:00:00+00:00", "2022-02-15T00:00:00+00:00", "2022-02-16T00:00:00+00:00", "2022-02-17T00:00:00+00:00", "2022-02-18T00:00:00+00:00", "2022-02-19T00:00:00+00:00", "2022-02-20T00:00:00+00:00", "2022-02-21T00:00:00+00:00", "2022-02-22T00:00:00+00:00", "2022-02-23T00:00:00+00:00", "2022-02-24T00:00:00+00:00", "2022-02-25T00:00:00+00:00", "2022-02-26T00:00:00+00:00", "2022-02-27T00:00:00+00:00", "2022-02-28T00:00:00+00:00", "2022-03-01T00:00:00+00:00", "2022-03-02T00:00:00+00:00", "2022-03-03T00:00:00+00:00", "2022-03-04T00:00:00+00:00", "2022-03-05T00:00:00+00:00", "2022-03-06T00:00:00+00:00", "2022-03-07T00:00:00+00:00", "2022-03-08T00:00:00+00:00", "2022-03-09T00:00:00+00:00", "2022-03-10T00:00:00+00:00", "2022-03-11T00:00:00+00:00", "2022-03-12T00:00:00+00:00", "2022-03-13T00:00:00+00:00", "2022-03-14T00:00:00+00:00", "2022-03-15T00:00:00+00:00", "2022-03-16T00:00:00+00:00", "2022-03-17T00:00:00+00:00", "2022-03-18T00:00:00+00:00", "2022-03-19T00:00:00+00:00", "2022-03-20T00:00:00+00:00", "2022-03-21T00:00:00+00:00", "2022-03-22T00:00:00+00:00", "2022-03-23T00:00:00+00:00", "2022-03-24T00:00:00+00:00", "2022-03-25T00:00:00+00:00", "2022-03-26T00:00:00+00:00", "2022-03-27T00:00:00+00:00", "2022-03-28T00:00:00+00:00", "2022-03-29T00:00:00+00:00", "2022-03-30T00:00:00+00:00", "2022-03-31T00:00:00+00:00", "2022-04-01T00:00:00+00:00", "2022-04-02T00:00:00+00:00", "2022-04-03T00:00:00+00:00", "2022-04-04T00:00:00+00:00", "2022-04-05T00:00:00+00:00", "2022-04-06T00:00:00+00:00", "2022-04-07T00:00:00+00:00", "2022-04-08T00:00:00+00:00", "2022-04-09T00:00:00+00:00", "2022-04-10T00:00:00+00:00", "2022-04-11T00:00:00+00:00", "2022-04-12T00:00:00+00:00", "2022-04-13T00:00:00+00:00", "2022-04-14T00:00:00+00:00", "2022-04-15T00:00:00+00:00", "2022-04-16T00:00:00+00:00", "2022-04-17T00:00:00+00:00", "2022-04-18T00:00:00+00:00", "2022-04-19T00:00:00+00:00", "2022-04-20T00:00:00+00:00", "2022-04-21T00:00:00+00:00", "2022-04-22T00:00:00+00:00", "2022-04-23T00:00:00+00:00", "2022-04-24T00:00:00+00:00", "2022-04-25T00:00:00+00:00", "2022-04-26T00:00:00+00:00", "2022-04-27T00:00:00+00:00", "2022-04-28T00:00:00+00:00", "2022-04-29T00:00:00+00:00", "2022-04-30T00:00:00+00:00", "2022-05-01T00:00:00+00:00", "2022-05-02T00:00:00+00:00", "2022-05-03T00:00:00+00:00", "2022-05-04T00:00:00+00:00", "2022-05-05T00:00:00+00:00", "2022-05-06T00:00:00+00:00", "2022-05-07T00:00:00+00:00", "2022-05-08T00:00:00+00:00", "2022-05-09T00:00:00+00:00", "2022-05-10T00:00:00+00:00", "2022-05-11T00:00:00+00:00", "2022-05-12T00:00:00+00:00", "2022-05-13T00:00:00+00:00", "2022-05-14T00:00:00+00:00", "2022-05-15T00:00:00+00:00", "2022-05-16T00:00:00+00:00", "2022-05-17T00:00:00+00:00", "2022-05-18T00:00:00+00:00", "2022-05-19T00:00:00+00:00", "2022-05-20T00:00:00+00:00", "2022-05-21T00:00:00+00:00", "2022-05-22T00:00:00+00:00", "2022-05-23T00:00:00+00:00", "2022-05-24T00:00:00+00:00", "2022-05-25T00:00:00+00:00", "2022-05-26T00:00:00+00:00", "2022-05-27T00:00:00+00:00", "2022-05-28T00:00:00+00:00", "2022-05-29T00:00:00+00:00", "2022-05-30T00:00:00+00:00", "2022-05-31T00:00:00+00:00", "2022-06-01T00:00:00+00:00", "2022-06-02T00:00:00+00:00", "2022-06-03T00:00:00+00:00", "2022-06-04T00:00:00+00:00", "2022-06-05T00:00:00+00:00", "2022-06-06T00:00:00+00:00", "2022-06-07T00:00:00+00:00", "2022-06-08T00:00:00+00:00", "2022-06-09T00:00:00+00:00", "2022-06-10T00:00:00+00:00", "2022-06-11T00:00:00+00:00", "2022-06-12T00:00:00+00:00", "2022-06-13T00:00:00+00:00", "2022-06-14T00:00:00+00:00", "2022-06-15T00:00:00+00:00", "2022-06-16T00:00:00+00:00", "2022-06-17T00:00:00+00:00", "2022-06-18T00:00:00+00:00", "2022-06-19T00:00:00+00:00", "2022-06-20T00:00:00+00:00", "2022-06-21T00:00:00+00:00", "2022-06-22T00:00:00+00:00", "2022-06-23T00:00:00+00:00", "2022-06-24T00:00:00+00:00", "2022-06-25T00:00:00+00:00", "2022-06-26T00:00:00+00:00", "2022-06-27T00:00:00+00:00", "2022-06-28T00:00:00+00:00", "2022-06-29T00:00:00+00:00", "2022-06-30T00:00:00+00:00", "2022-07-01T00:00:00+00:00", "2022-07-02T00:00:00+00:00", "2022-07-03T00:00:00+00:00", "2022-07-04T00:00:00+00:00", "2022-07-05T00:00:00+00:00", "2022-07-06T00:00:00+00:00", "2022-07-07T00:00:00+00:00", "2022-07-08T00:00:00+00:00", "2022-07-09T00:00:00+00:00", "2022-07-10T00:00:00+00:00", "2022-07-11T00:00:00+00:00", "2022-07-12T00:00:00+00:00", "2022-07-13T00:00:00+00:00", "2022-07-14T00:00:00+00:00", "2022-07-15T00:00:00+00:00", "2022-07-16T00:00:00+00:00", "2022-07-17T00:00:00+00:00", "2022-07-18T00:00:00+00:00", "2022-07-19T00:00:00+00:00", "2022-07-20T00:00:00+00:00", "2022-07-21T00:00:00+00:00", "2022-07-22T00:00:00+00:00", "2022-07-23T00:00:00+00:00", "2022-07-24T00:00:00+00:00", "2022-07-25T00:00:00+00:00", "2022-07-26T00:00:00+00:00", "2022-07-27T00:00:00+00:00", "2022-07-28T00:00:00+00:00", "2022-07-29T00:00:00+00:00", "2022-07-30T00:00:00+00:00", "2022-07-31T00:00:00+00:00", "2022-08-01T00:00:00+00:00", "2022-08-02T00:00:00+00:00", "2022-08-03T00:00:00+00:00", "2022-08-04T00:00:00+00:00", "2022-08-05T00:00:00+00:00", "2022-08-06T00:00:00+00:00", "2022-08-07T00:00:00+00:00", "2022-08-08T00:00:00+00:00", "2022-08-09T00:00:00+00:00", "2022-08-10T00:00:00+00:00", "2022-08-11T00:00:00+00:00", "2022-08-12T00:00:00+00:00", "2022-08-13T00:00:00+00:00", "2022-08-14T00:00:00+00:00", "2022-08-15T00:00:00+00:00", "2022-08-16T00:00:00+00:00", "2022-08-17T00:00:00+00:00", "2022-08-18T00:00:00+00:00", "2022-08-19T00:00:00+00:00", "2022-08-20T00:00:00+00:00", "2022-08-21T00:00:00+00:00", "2022-08-22T00:00:00+00:00", "2022-08-23T00:00:00+00:00", "2022-08-24T00:00:00+00:00", "2022-08-25T00:00:00+00:00", "2022-08-26T00:00:00+00:00", "2022-08-27T00:00:00+00:00", "2022-08-28T00:00:00+00:00", "2022-08-29T00:00:00+00:00", "2022-08-30T00:00:00+00:00", "2022-08-31T00:00:00+00:00", "2022-09-01T00:00:00+00:00", "2022-09-02T00:00:00+00:00", "2022-09-03T00:00:00+00:00", "2022-09-04T00:00:00+00:00", "2022-09-05T00:00:00+00:00", "2022-09-06T00:00:00+00:00", "2022-09-07T00:00:00+00:00", "2022-09-08T00:00:00+00:00", "2022-09-09T00:00:00+00:00", "2022-09-10T00:00:00+00:00", "2022-09-11T00:00:00+00:00", "2022-09-12T00:00:00+00:00", "2022-09-13T00:00:00+00:00", "2022-09-14T00:00:00+00:00", "2022-09-15T00:00:00+00:00", "2022-09-16T00:00:00+00:00", "2022-09-17T00:00:00+00:00", "2022-09-18T00:00:00+00:00", "2022-09-19T00:00:00+00:00", "2022-09-20T00:00:00+00:00", "2022-09-21T00:00:00+00:00", "2022-09-22T00:00:00+00:00", "2022-09-23T00:00:00+00:00", "2022-09-24T00:00:00+00:00", "2022-09-25T00:00:00+00:00", "2022-09-26T00:00:00+00:00", "2022-09-27T00:00:00+00:00", "2022-09-28T00:00:00+00:00", "2022-09-29T00:00:00+00:00", "2022-09-30T00:00:00+00:00", "2022-10-01T00:00:00+00:00", "2022-10-02T00:00:00+00:00", "2022-10-03T00:00:00+00:00", "2022-10-04T00:00:00+00:00", "2022-10-05T00:00:00+00:00", "2022-10-06T00:00:00+00:00", "2022-10-07T00:00:00+00:00", "2022-10-08T00:00:00+00:00", "2022-10-09T00:00:00+00:00", "2022-10-10T00:00:00+00:00", "2022-10-11T00:00:00+00:00", "2022-10-12T00:00:00+00:00", "2022-10-13T00:00:00+00:00", "2022-10-14T00:00:00+00:00", "2022-10-15T00:00:00+00:00", "2022-10-16T00:00:00+00:00", "2022-10-17T00:00:00+00:00", "2022-10-18T00:00:00+00:00", "2022-10-19T00:00:00+00:00", "2022-10-20T00:00:00+00:00", "2022-10-21T00:00:00+00:00", "2022-10-22T00:00:00+00:00", "2022-10-23T00:00:00+00:00", "2022-10-24T00:00:00+00:00", "2022-10-25T00:00:00+00:00", "2022-10-26T00:00:00+00:00", "2022-10-27T00:00:00+00:00", "2022-10-28T00:00:00+00:00", "2022-10-29T00:00:00+00:00", "2022-10-30T00:00:00+00:00", "2022-10-31T00:00:00+00:00", "2022-11-01T00:00:00+00:00", "2022-11-02T00:00:00+00:00", "2022-11-03T00:00:00+00:00", "2022-11-04T00:00:00+00:00", "2022-11-05T00:00:00+00:00", "2022-11-06T00:00:00+00:00", "2022-11-07T00:00:00+00:00", "2022-11-08T00:00:00+00:00", "2022-11-09T00:00:00+00:00", "2022-11-10T00:00:00+00:00", "2022-11-11T00:00:00+00:00", "2022-11-12T00:00:00+00:00", "2022-11-13T00:00:00+00:00", "2022-11-14T00:00:00+00:00", "2022-11-15T00:00:00+00:00", "2022-11-16T00:00:00+00:00", "2022-11-17T00:00:00+00:00", "2022-11-18T00:00:00+00:00", "2022-11-19T00:00:00+00:00", "2022-11-20T00:00:00+00:00", "2022-11-21T00:00:00+00:00", "2022-11-22T00:00:00+00:00", "2022-11-23T00:00:00+00:00", "2022-11-24T00:00:00+00:00", "2022-11-25T00:00:00+00:00", "2022-11-26T00:00:00+00:00", "2022-11-27T00:00:00+00:00", "2022-11-28T00:00:00+00:00", "2022-11-29T00:00:00+00:00", "2022-11-30T00:00:00+00:00", "2022-12-01T00:00:00+00:00", "2022-12-02T00:00:00+00:00", "2022-12-03T00:00:00+00:00", "2022-12-04T00:00:00+00:00", "2022-12-05T00:00:00+00:00", "2022-12-06T00:00:00+00:00", "2022-12-07T00:00:00+00:00", "2022-12-08T00:00:00+00:00", "2022-12-09T00:00:00+00:00", "2022-12-10T00:00:00+00:00", "2022-12-11T00:00:00+00:00", "2022-12-12T00:00:00+00:00", "2022-12-13T00:00:00+00:00", "2022-12-14T00:00:00+00:00", "2022-12-15T00:00:00+00:00", "2022-12-16T00:00:00+00:00", "2022-12-17T00:00:00+00:00", "2022-12-18T00:00:00+00:00", "2022-12-19T00:00:00+00:00", "2022-12-20T00:00:00+00:00", "2022-12-21T00:00:00+00:00", "2022-12-22T00:00:00+00:00", "2022-12-23T00:00:00+00:00", "2022-12-24T00:00:00+00:00", "2022-12-25T00:00:00+00:00", "2022-12-26T00:00:00+00:00", "2022-12-27T00:00:00+00:00", "2022-12-28T00:00:00+00:00", "2022-12-29T00:00:00+00:00", "2022-12-30T00:00:00+00:00", "2022-12-31T00:00:00+00:00", "2023-01-01T00:00:00+00:00", "2023-01-02T00:00:00+00:00", "2023-01-03T00:00:00+00:00", "2023-01-04T00:00:00+00:00", "2023-01-05T00:00:00+00:00", "2023-01-06T00:00:00+00:00", "2023-01-07T00:00:00+00:00", "2023-01-08T00:00:00+00:00", "2023-01-09T00:00:00+00:00", "2023-01-10T00:00:00+00:00", "2023-01-11T00:00:00+00:00", "2023-01-12T00:00:00+00:00", "2023-01-13T00:00:00+00:00", "2023-01-14T00:00:00+00:00", "2023-01-15T00:00:00+00:00", "2023-01-16T00:00:00+00:00", "2023-01-17T00:00:00+00:00", "2023-01-18T00:00:00+00:00", "2023-01-19T00:00:00+00:00", "2023-01-20T00:00:00+00:00", "2023-01-21T00:00:00+00:00", "2023-01-22T00:00:00+00:00", "2023-01-23T00:00:00+00:00", "2023-01-24T00:00:00+00:00", "2023-01-25T00:00:00+00:00", "2023-01-26T00:00:00+00:00", "2023-01-27T00:00:00+00:00", "2023-01-28T00:00:00+00:00", "2023-01-29T00:00:00+00:00", "2023-01-30T00:00:00+00:00", "2023-01-31T00:00:00+00:00", "2023-02-01T00:00:00+00:00", "2023-02-02T00:00:00+00:00", "2023-02-03T00:00:00+00:00", "2023-02-04T00:00:00+00:00", "2023-02-05T00:00:00+00:00", "2023-02-06T00:00:00+00:00", "2023-02-07T00:00:00+00:00", "2023-02-08T00:00:00+00:00", "2023-02-09T00:00:00+00:00", "2023-02-10T00:00:00+00:00", "2023-02-11T00:00:00+00:00", "2023-02-12T00:00:00+00:00", "2023-02-13T00:00:00+00:00", "2023-02-14T00:00:00+00:00", "2023-02-15T00:00:00+00:00", "2023-02-16T00:00:00+00:00", "2023-02-17T00:00:00+00:00", "2023-02-18T00:00:00+00:00", "2023-02-19T00:00:00+00:00", "2023-02-20T00:00:00+00:00", "2023-02-21T00:00:00+00:00", "2023-02-22T00:00:00+00:00", "2023-02-23T00:00:00+00:00", "2023-02-24T00:00:00+00:00", "2023-02-25T00:00:00+00:00", "2023-02-26T00:00:00+00:00", "2023-02-27T00:00:00+00:00", "2023-02-28T00:00:00+00:00", "2023-03-01T00:00:00+00:00", "2023-03-02T00:00:00+00:00", "2023-03-03T00:00:00+00:00", "2023-03-04T00:00:00+00:00", "2023-03-05T00:00:00+00:00", "2023-03-06T00:00:00+00:00", "2023-03-07T00:00:00+00:00", "2023-03-08T00:00:00+00:00", "2023-03-09T00:00:00+00:00", "2023-03-10T00:00:00+00:00", "2023-03-11T00:00:00+00:00", "2023-03-12T00:00:00+00:00", "2023-03-13T00:00:00+00:00", "2023-03-14T00:00:00+00:00", "2023-03-15T00:00:00+00:00", "2023-03-16T00:00:00+00:00", "2023-03-17T00:00:00+00:00", "2023-03-18T00:00:00+00:00", "2023-03-19T00:00:00+00:00", "2023-03-20T00:00:00+00:00", "2023-03-21T00:00:00+00:00", "2023-03-22T00:00:00+00:00", "2023-03-23T00:00:00+00:00", "2023-03-24T00:00:00+00:00", "2023-03-25T00:00:00+00:00", "2023-03-26T00:00:00+00:00", "2023-03-27T00:00:00+00:00", "2023-03-28T00:00:00+00:00", "2023-03-29T00:00:00+00:00", "2023-03-30T00:00:00+00:00", "2023-03-31T00:00:00+00:00", "2023-04-01T00:00:00+00:00", "2023-04-02T00:00:00+00:00", "2023-04-03T00:00:00+00:00", "2023-04-04T00:00:00+00:00", "2023-04-05T00:00:00+00:00", "2023-04-06T00:00:00+00:00", "2023-04-07T00:00:00+00:00", "2023-04-08T00:00:00+00:00", "2023-04-09T00:00:00+00:00", "2023-04-10T00:00:00+00:00", "2023-04-11T00:00:00+00:00", "2023-04-12T00:00:00+00:00", "2023-04-13T00:00:00+00:00", "2023-04-14T00:00:00+00:00", "2023-04-15T00:00:00+00:00", "2023-04-16T00:00:00+00:00", "2023-04-17T00:00:00+00:00", "2023-04-18T00:00:00+00:00", "2023-04-19T00:00:00+00:00", "2023-04-20T00:00:00+00:00", "2023-04-21T00:00:00+00:00", "2023-04-22T00:00:00+00:00", "2023-04-23T00:00:00+00:00", "2023-04-24T00:00:00+00:00", "2023-04-25T00:00:00+00:00", "2023-04-26T00:00:00+00:00", "2023-04-27T00:00:00+00:00", "2023-04-28T00:00:00+00:00", "2023-04-29T00:00:00+00:00", "2023-04-30T00:00:00+00:00", "2023-05-01T00:00:00+00:00", "2023-05-02T00:00:00+00:00", "2023-05-03T00:00:00+00:00", "2023-05-04T00:00:00+00:00", "2023-05-05T00:00:00+00:00", "2023-05-06T00:00:00+00:00", "2023-05-07T00:00:00+00:00", "2023-05-08T00:00:00+00:00", "2023-05-09T00:00:00+00:00", "2023-05-10T00:00:00+00:00", "2023-05-11T00:00:00+00:00", "2023-05-12T00:00:00+00:00", "2023-05-13T00:00:00+00:00", "2023-05-14T00:00:00+00:00", "2023-05-15T00:00:00+00:00", "2023-05-16T00:00:00+00:00", "2023-05-17T00:00:00+00:00", "2023-05-18T00:00:00+00:00", "2023-05-19T00:00:00+00:00", "2023-05-20T00:00:00+00:00", "2023-05-21T00:00:00+00:00", "2023-05-22T00:00:00+00:00", "2023-05-23T00:00:00+00:00", "2023-05-24T00:00:00+00:00", "2023-05-25T00:00:00+00:00", "2023-05-26T00:00:00+00:00", "2023-05-27T00:00:00+00:00", "2023-05-28T00:00:00+00:00", "2023-05-29T00:00:00+00:00", "2023-05-30T00:00:00+00:00"], "xaxis": "x", "y": [49.48250389099121, 49.48500442504883, 49.48125425974528, 49.471253871917725, 49.4933377901713, 49.49083757400513, 49.50167099634806, 49.510836919148765, 49.51542059580485, 49.49708779652914, 49.502504189809166, 49.49250411987305, 49.50792074203491, 49.49250396092733, 49.524587313334145, 49.50208727518717, 49.50917100906372, 49.51208750406901, 49.51958719889323, 49.51125399271647, 49.51292069753011, 49.534170945485435, 49.50958760579427, 49.51167106628418, 49.51583687464396, 49.503753980000816, 49.530004024505615, 49.53375371297201, 49.52375396092733, 49.51875368754069, 49.51833709081014, 49.53292067845663, 49.53208748499552, 49.53042062123617, 49.53167072931925, 49.53583733240763, 49.53458722432455, 49.536670525868736, 49.54417037963867, 49.54417053858439, 49.53375403086344, 49.530837059020996, 49.541253884633385, 49.55042060216268, 49.56042035420736, 49.535420417785645, 49.537504037221275, 49.55458720525106, 49.53250376383463, 49.55208683013916, 49.53917074203491, 49.54000393549601, 49.55042060216268, 49.525420824686684, 49.48292048772176, 49.53583780924479, 49.52167065938314, 49.53958749771118, 49.5587542851766, 49.54250399271647, 49.56125418345133, 49.54292090733846, 49.536670207977295, 49.566670417785645, 49.557504177093506, 49.57875442504883, 49.57875410715739, 49.59542067845663, 49.5479211807251, 49.554171085357666, 49.557503859202065, 49.584170977274574, 49.552504221598305, 49.57417074839274, 49.596670627593994, 49.56042114893595, 49.618754386901855, 49.58208735783895, 49.53792063395182, 49.52333736419678, 49.53917074203491, 49.52250369389852, 49.56292072931925, 49.5183375676473, 49.5225043296814, 49.51958703994751, 49.506254037221275, 49.57583729426066, 49.4837539990743, 49.48875411351522, 49.49417098363241, 49.49125369389852, 49.48500378926595, 49.42542060216268, 49.43458731969198, 49.42667055130005, 49.45417070388794, 49.449587186177574, 49.39375400543213, 49.40000375111898, 49.37125380833944, 49.41417074203491, 49.434587160746254, 49.44208685557047, 49.437503814697266, 49.43500407536825, 49.417503356933594, 49.40083694458008, 49.39625390370687, 49.38958724339803, 49.41667032241821, 49.370420138041176, 49.377920150756836, 49.371253967285156, 49.38625351587931, 49.36625369389852, 49.36000394821167, 49.35542058944702, 49.340420405069985, 49.32958698272705, 49.341253916422524, 49.31417067845663, 49.312920888264976, 49.314170360565186, 49.32875394821167, 49.352087338765465, 49.31750377019247, 49.30000368754069, 49.285837491353355, 49.29083728790283, 49.312920570373535, 49.32583713531494, 49.30250342686971, 49.28958717981974, 49.26292037963867, 49.30250406265259, 49.35417032241821, 49.347503662109375, 49.302920500437416, 49.308336893717446, 49.32500394185384, 49.32417074839274, 49.32041994730631, 49.329170068105064, 49.33625411987305, 49.3283371925354, 49.32500394185384, 49.33583688735962, 49.3308375676473, 49.32792059580485, 49.32042042414347, 49.30042044321696, 49.29750378926595, 49.31542078653971, 49.33833694458008, 49.338337421417236, 49.33708715438843, 49.34583727518717, 49.349170207977295, 49.34583727518717, 49.35083723068237, 49.375003814697266, 49.39333709081014, 49.36917050679525, 49.382920583089195, 49.405420462290444, 49.39250389734904, 49.415003617604576, 49.42167011896769, 49.43833700815836, 49.421253522237144, 49.414169788360596, 49.42083660761515, 49.43750365575155, 49.43708721796671, 49.444587071736656, 49.430003801981606, 49.42042016983032, 49.45875374476115, 49.44167057673136, 49.451253732045494, 49.42667023340861, 49.433336893717446, 49.44125318527222, 49.42458629608154, 49.45375394821167, 49.42333698272705, 49.42750374476115, 49.440420150756836, 49.47583723068237, 49.51125431060791, 49.51458756128947, 49.52875407536825, 49.5016705195109, 49.493337313334145, 49.4879207611084, 49.476253827412926, 49.4625039100647, 49.47000392278036, 49.468753496805824, 49.45875358581543, 49.448336919148765, 49.465003967285156, 49.50792074203491, 49.494170347849526, 49.472087383270264, 49.4500036239624, 49.44041999181112, 49.440003077189125, 49.447087128957115, 49.449586709340416, 49.439586321512856, 49.41583665211996, 49.39958747227987, 49.40250333150228, 49.38875341415405, 49.41375319163004, 49.386253674825035, 49.365837256113686, 49.38000377019247, 49.38208691279093, 49.35583702723185, 49.33042017618815, 49.35333728790283, 49.345837116241455, 49.372503439585365, 49.36667029062907, 49.37167056401571, 49.402503649393715, 49.389169692993164, 49.40042002995809, 49.433753967285156, 49.409587383270264, 49.39083687464396, 49.39583683013916, 49.38708702723185, 49.38625415166219, 49.3966703414917, 49.39292033513387, 49.37500365575155, 49.381253719329834, 49.38333702087402, 49.386253674825035, 49.371670405069985, 49.35500383377075, 49.39167022705078, 49.37667067845663, 49.37542041142782, 49.35792048772176, 49.38292010625204, 49.37417062123617, 49.36292060216268, 49.374587059020996, 49.33292039235433, 49.353753884633385, 49.360836823781334, 49.33583720525106, 49.34333737691244, 49.349586963653564, 49.3579208056132, 49.35958703358968, 49.40375328063965, 49.43375317255656, 49.416253407796226, 49.41333627700806, 49.38958692550659, 49.38958708445231, 49.40042018890381, 49.39375321070353, 49.40542030334473, 49.39625326792399, 49.41292015711466, 49.40042002995809, 49.42333666483561, 49.42208687464396, 49.39583667119344, 49.40875323613485, 49.40083662668864, 49.415836811065674, 49.398753801981606, 49.389586766560875, 49.42458709081014, 49.41042025883993, 49.40125322341919, 49.42167043685913, 49.4329202969869, 49.424586613972984, 49.45791975657145, 49.45500342051188, 49.45667028427124, 49.44750356674194, 49.44708665211996, 49.43042023976644, 49.45333703358968, 49.454586823781334, 49.45542081197103, 49.45583724975586, 49.46708758672079, 49.44750372568766, 49.45708688100179, 49.452086766560875, 49.4608367284139, 49.45583693186442, 49.44916979471842, 49.4591703414917, 49.45125357309977, 49.437920570373535, 49.46167039871216, 49.466253916422524, 49.48750400543213, 49.46042124430338, 49.48000446955363, 49.46458673477173, 49.477920850118004, 49.475837548573814, 49.45667028427124, 49.45583709081014, 49.46708758672079, 49.45833762486776, 49.468753814697266, 49.45333735148112, 49.4483372370402, 49.45917065938314, 49.471670627593994, 49.48042074839274, 49.465837478637695, 49.4516708056132, 49.45125420888265, 49.45667028427124, 49.465837478637695, 49.443753719329834, 49.470003604888916, 49.44958750406901, 49.44875351587931, 49.46292098363241, 49.47583723068237, 49.467920462290444, 49.43542035420736, 49.457503954569496, 49.49208768208822, 49.46542024612427, 49.46542056401571, 49.5083376566569, 49.48083734512329, 49.495837688446045, 49.46542088190714, 49.47083759307861, 49.47417068481445, 49.47833760579427, 49.476254304250084, 49.47833792368571, 49.47583770751953, 49.51750421524048, 49.508337338765465, 49.5141708056132, 49.51167090733846, 49.505003929138184, 49.500004291534424, 49.52333720525106, 49.50208759307861, 49.50875393549601, 49.49667056401571, 49.51000420252482, 49.52000411351522, 49.50083748499552, 49.527920722961426, 49.51667086283366, 49.522087256113686, 49.53125365575155, 49.54333734512329, 49.531253496805824, 49.52583726247152, 49.532087326049805, 49.54833714167277, 49.52042055130005, 49.531670252482094, 49.52000411351522, 49.535420417785645, 49.53458706537882, 49.52958758672079, 49.52083698908488, 49.57167069117228, 49.526670932769775, 49.54083728790283, 49.557087421417236, 49.55500364303589, 49.54375410079956, 49.54583740234375, 49.537504037221275, 49.52917035420736, 49.533754189809166, 49.54292074839274, 49.560004234313965, 49.539170583089195, 49.54375378290812, 49.5387544631958, 49.50625387827555, 49.540004094441734, 49.55000432332357, 49.57625420888265, 49.56792084376017, 49.56542046864828, 49.54958709081014, 49.56708780924479, 49.589170614878334, 49.565837701161705, 49.582921187082924, 49.57417058944702, 49.57750447591146, 49.599587281545006, 49.57375399271647, 49.58708747227987, 49.567504247029625, 49.56208769480387, 49.57292079925537, 49.57667096455892, 49.58667119344076, 49.55667098363241, 49.560837745666504, 49.57792091369629, 49.57708724339803, 49.567087491353355, 49.58208735783895, 49.57792091369629, 49.55667066574097, 49.56417067845663, 49.55917104085287, 49.59208758672079, 49.56083742777506, 49.55792109171549, 49.565420627593994, 49.56042114893595, 49.56000407536825, 49.55917056401571, 49.55375385284424, 49.54458729426066, 49.55833705266317, 49.55250438054403, 49.54833730061849, 49.54292074839274, 49.55625422795614, 49.53792095184326, 49.54417053858439, 49.51292069753011, 49.53917074203491, 49.52833731969198, 49.53208716710409, 49.51667070388794, 49.502920627593994, 49.511670430501304, 49.52125406265259, 49.510420640309654, 49.48292064666748, 49.4770876566569, 49.47958755493164, 49.45833698908488, 49.44375387827555, 49.42958688735962, 49.42417001724243, 49.42125368118286, 49.414586861928306, 49.40083694458008, 49.417086601257324, 49.41333738962809, 49.40333731969198, 49.40125370025635, 49.40833727518717, 49.3875036239624, 49.41333723068237, 49.39583698908488, 49.380419890085854, 49.374170780181885, 49.38417053222656, 49.39875396092733, 49.340420405069985, 49.354587395985924, 49.324587186177574, 49.375420252482094, 49.35250393549601, 49.33208703994751, 49.34917084376017, 49.335837046305336, 49.33417018254598, 49.34792025883993, 49.34208742777506, 49.32417090733846, 49.34917036692301, 49.28000259399414, 49.28000259399414, 49.28000259399414, 49.28000259399414, 49.26958608627319, 49.23958651224772, 49.229586124420166, 49.21875270207723, 49.261252562204994, 49.24791955947876, 49.23291937510172, 49.23708629608154, 49.23291937510172, 49.225003242492676, 49.201253255208336, 49.18708642323812, 49.17833646138509, 49.15916999181112, 49.16625356674194, 49.14375368754069, 49.150420347849526, 49.12167024612427, 49.130003452301025, 49.13375377655029, 49.11208693186442, 49.12000354131063, 49.1037532488505, 49.10291989644369, 49.10625298817953, 49.07583602269491, 49.08541933695475, 49.09791978200277, 49.08333603541056, 49.08875322341919, 49.30583635965983, 49.30333614349365, 49.334585984547935, 49.349169890085854, 49.31291913986206, 49.28000275293986, 49.26500256856283, 49.24916950861613, 49.26875273386637, 49.2733359336853, 49.206669648488365, 49.225419680277504, 49.24666961034139, 49.23041979471842, 49.23625294367472, 49.235836346944176, 49.236669381459556, 49.21167008082072, 49.23041979471842, 49.227919578552246, 49.22291946411133, 49.21291971206665, 49.290002504984535, 49.48041979471842, 49.43500280380249, 49.4183357556661, 49.386669953664146, 49.39041964213053, 49.40791924794515, 49.3704195022583, 49.47458664576212, 49.46625296274821, 49.42416954040527, 49.409169832865395, 49.392502784729004, 49.506253242492676, 49.48500299453735, 49.4379194577535, 49.45541922251383, 49.43958600362142, 49.44666973749796, 49.4108362197876, 49.378336111704506, 49.35708602269491, 49.350002924601235, 49.33666960398356, 49.33166980743408, 49.3450026512146, 49.353753089904785, 49.41416931152344, 49.443752924601235, 49.42500273386637, 49.4412530263265, 49.455003102620445, 49.461668968200684, 49.45583629608154, 49.44000291824341, 49.47875293095907, 49.46125332514445, 49.402919928232826, 49.40541934967041, 49.36291901270548, 49.40541934967041, 49.43791961669922, 49.38708623250326, 49.480419953664146, 49.61208629608154, 49.52166986465454, 49.47958644231161, 49.43833605448405, 49.431252797444664, 49.41208632787069, 49.40041939417521, 49.3854193687439, 49.354586124420166, 49.353752772013344, 49.334585984547935, 49.31208610534668, 49.31791909535726, 49.298752784729004, 49.29750315348307, 49.26958608627319, 49.26916980743408, 49.27875280380249, 49.26625283559164, 49.238752683003746, 49.24750280380249, 49.222502867380776, 49.19083627065023, 49.24250284830729, 49.22500308354696, 49.20958614349365, 49.17083660761515, 49.15291976928711, 49.16000318527222, 49.129586378733315, 49.1112535794576, 49.08041969935099, 49.10208670298258, 49.09708627065023, 49.110420068105064, 49.100836435953774, 49.09333658218384, 49.07333676020304, 49.10917011896769, 49.09458669026693, 49.07458623250326, 49.076670010884605, 49.08041969935099, 49.068752924601235, 49.0695865948995, 49.065003395080566, 49.070419470469155, 49.048752784729004, 49.08833599090576, 49.0612530708313, 49.06291993459066, 49.048336346944176, 49.017086346944176, 49.02291965484619, 48.989586194356285, 49.05500300725301, 49.06041924158732, 49.06833664576212, 49.09750318527222, 49.07875283559164, 49.13167015711466, 49.11333624521891, 49.10792016983032, 49.10291989644369, 49.103336334228516, 49.09667030970255, 49.075003465016685, 49.08250300089518, 49.09750270843506, 49.07208585739136, 49.08916966120402, 49.05250263214111, 49.09125328063965, 49.076670010884605, 49.10125319163004, 49.07875283559164, 49.044586181640625, 49.05291986465454, 49.08000183105469, 49.08000183105469, 49.08000183105469, 49.08000183105469, 49.047916412353516, 49.07874997456869, 49.080000241597496, 49.071250120798744, 49.06333335240682, 49.087916692097984, 49.08333349227905, 49.07041708628336, 49.071667194366455, 49.04333337148031, 49.08374961217245, 49.079166889190674, 49.07208299636841, 49.07708342870077, 49.081666787465416, 49.094166119893394, 49.08916632334391, 49.085000356038414, 49.08583354949951, 49.09041690826416, 49.10249980290731, 49.08166662851969, 49.1045831044515, 49.09499994913737, 49.05291732152303, 49.082500298817955, 49.09041674931844, 49.085416634877525, 49.09833367665609, 49.09416659673055, 49.08791653315226, 49.10333363215128, 49.08083359400431, 49.10958321889242, 49.09458303451538, 49.12541659673055, 49.12250026067098, 49.11875009536743, 49.12083339691162, 49.14874998728434, 49.130416552225746, 49.125832875569664, 49.117083390553795, 49.12333345413208, 49.13249969482422, 49.15708303451538, 49.14583317438761, 49.13958326975504, 49.13791672388712, 49.1479164759318, 49.12833340962728, 49.13499975204468, 49.139166514078774, 49.15583340326945, 49.15000009536743, 49.15083312988281, 49.164166609446205, 49.170416831970215, 49.14958349863688, 49.149166107177734, 49.13708305358887, 49.15916601816813, 49.15458297729492, 49.15416653951009, 49.15458313624064, 49.16791661580404, 49.19541676839193, 49.14541673660278, 49.16708326339722, 49.205833435058594, 49.18541638056437, 49.177083015441895, 49.20124991734823, 49.18666617075602, 49.19208319981893, 49.181666692097984, 49.20083347956339, 49.22041622797648, 49.19041649500529, 49.21166674296061, 49.20250002543131, 49.227083365122475, 49.19541613260905, 49.1883331934611, 49.228750228881836, 49.19000005722046, 49.21583334604899, 49.21708345413208, 49.20833349227905, 49.222500006357826, 49.18791627883911, 49.20875024795532, 49.22208340962728, 49.225833574930824, 49.23458353678385, 49.21750020980835, 49.19999980926514, 49.210833390553795, 49.2162504196167, 49.20416673024496, 49.20875040690104, 49.208333333333336, 49.2079168955485, 49.240416844685875, 49.201666514078774, 49.26124986012777, 49.22916682561239, 49.22500022252401, 49.22291692097982, 49.228333950042725, 49.25083335240682, 49.22499990463257, 49.23958349227905, 49.22333367665609, 49.23416646321615, 49.23791694641113, 49.20416704813639, 49.20083363850912, 49.24458360671997, 49.24541664123535, 49.21916659673055, 49.2179168065389, 49.23708359400431, 49.237083752950035, 49.231666564941406, 49.22958358128866, 49.2329166730245, 49.20791721343994, 49.233333587646484, 49.19208383560181, 49.20708306630453, 49.203333059946694, 49.23041693369547, 49.191666762034096, 49.19708283742269, 49.1808336575826, 49.19541676839193, 49.165833473205566, 49.20583359400431, 49.172916889190674, 49.15541664759318, 49.18374983469645, 49.11625003814697, 49.11375013987223, 49.11833349863688, 49.130417029062905, 49.17208321889242, 49.11875025431315, 49.13666693369547, 49.108333587646484, 49.15541696548462, 49.15374994277954, 49.11583344141642, 49.10666640599569, 49.1166664759318, 49.109583695729576, 49.110416412353516, 49.08750009536743, 49.06833362579346, 49.09624989827474, 49.06041701634725, 49.09250020980835, 49.081250031789146, 49.0083335240682, 49.075833320617676, 49.06166696548462, 49.05416695276896, 49.021249771118164, 49.02833382288615, 48.99291658401489, 49.020833333333336, 48.99708350499471, 49.006667137145996, 49.02833318710327, 49.00000015894572, 49.01291672388712, 48.983333587646484, 48.96083386739095, 48.97999986012777, 48.98416709899902, 48.946250120798744, 48.98541657129923, 48.987083752950035, 48.94375069936117, 49.006250063578285, 48.958750089009605, 48.91541655858358, 48.94541676839193, 48.95083347956339, 48.94666655858358, 48.95374997456869, 48.95833365122477, 48.93666648864746, 48.931666692097984, 48.90333318710327, 48.90625031789144, 48.93749984105428, 48.89625040690104, 48.8970832824707, 48.90958293279012, 48.895833015441895, 48.890833377838135, 48.86166683832804, 48.87666686375936, 48.889166831970215, 48.884166876475014, 48.884583473205566, 48.86958328882853, 48.88875023523966, 48.88291692733765, 48.865416844685875, 48.900833447774254, 48.89208364486694, 48.88791672388712, 48.88708337148031, 48.860000133514404, 48.888333320617676, 48.9175001780192, 48.88041687011719, 48.90999968846639, 48.91874980926514, 48.9258337020874, 48.94750006993612, 48.95208326975504, 48.97499974568685, 48.97666708628336, 48.9679168065389, 48.978750228881836, 48.96750005086263, 48.98833338419596, 49.00500011444092, 49.02333354949951, 49.04374980926514, 49.05875015258789, 49.04708321889242, 49.061249574025474, 49.052083333333336, 49.046249866485596, 49.05625009536743, 49.03916645050049, 49.07750002543131, 49.08166694641113, 49.09541670481364, 49.115416526794434, 49.10333331425985, 49.137082735697426, 49.15166632334391, 49.1470832824707, 49.15458313624064, 49.12916692097982, 49.14291636149088, 49.134166399637856, 49.15583324432373, 49.12416648864746, 49.06083345413208, 49.10458358128866, 49.119166692097984, 49.137916564941406, 49.12458308537801, 49.12333329518636, 49.13416655858358, 49.10458326339722, 49.06083313624064, 49.087916692097984, 49.11291694641113, 49.083750089009605, 49.07833274205526, 49.09291664759318, 49.082500298817955, 49.086666425069176, 49.08166662851969, 49.087916215260826, 49.10375006993612, 49.11333338419596, 49.09000031153361, 49.09208329518636, 49.096249421437584, 49.0945831934611, 49.076666037241615, 49.078333377838135, 49.06875022252401, 49.03541692097982, 49.06125020980835, 49.09666649500529, 49.05541658401489, 49.069583574930824, 49.038333574930824, 49.050833225250244, 49.04583326975504, 49.03041664759318, 49.04166634877523, 49.06583325068156, 49.083333333333336, 49.051666577657066, 49.09583314259847, 49.07541672388712, 49.06666692097982, 49.075833002726235, 49.070833365122475, 49.09291648864746, 49.08041636149088, 49.085833390553795, 49.07958364486694, 49.05749988555908, 49.055416901906334, 49.054583390553795, 49.0516668955485, 49.075833797454834, 49.068750063578285, 49.063750425974526, 49.046666304270424, 49.06166664759318, 49.03125031789144, 49.06124941507975, 49.0304168065389, 49.03541660308838, 49.02458302179972, 49.03166691462199, 49.00750017166138, 49.02583312988281, 49.01333347956339, 49.04666678110758, 49.056666692097984, 49.08458344141642, 49.08083311716715, 49.065833568573, 49.071666399637856, 49.05458291371664, 49.05625009536743, 49.085416634877525, 49.076666514078774, 49.08374993006388, 49.098333517710365, 49.10958290100098, 49.11166636149088, 49.11041657129923, 49.11208327611288, 49.11583296457926, 49.1029167175293, 49.05124966303507, 49.08208386103312, 49.0854164759318, 49.153333028157554, 49.1258331934611, 49.113749980926514, 49.12374989191691, 49.15416685740153, 49.22541650136312, 49.23749987284342, 49.098750591278076, 49.14333359400431, 49.115833123524986, 49.137083530426025, 49.10624965031942, 49.163749853769936, 49.17833375930786, 49.12291653951009, 49.16874965031942, 49.144583225250244, 49.15541648864746, 49.13750012715658, 49.12458324432373, 49.138749758402504, 49.1554168065389, 49.13000011444092, 49.15291674931844, 49.13500006993612, 49.14666668574015, 49.134583155314125, 49.12416664759318, 49.149999936421715, 49.16624959309896, 49.16083319981893, 49.17291657129923, 49.14999977747599, 49.1362501780192, 49.16041692097982, 49.169166247049965, 49.15958325068156, 49.15291674931844, 49.140416304270424, 49.13708289464315, 49.16124979654948, 49.173333168029785, 49.13208341598511, 49.18416659037272, 49.19624996185303, 49.20916668574015, 49.1445837020874, 49.169583320617676, 49.1733333269755, 49.16166671117147, 49.15458313624064, 49.180000146230064, 49.172083377838135, 49.182082653045654, 49.22166713078817, 49.21958303451538, 49.15166680018107, 49.184583028157554, 49.1700005531311, 49.19833326339722, 49.10541661580404, 49.20708338419596, 49.184999783833824, 49.227500120798744, 49.21000019709269, 49.20874993006388, 49.16208346684774, 49.1883331934611, 49.16083335876465, 49.21708297729492, 49.18999989827474, 49.17958307266235, 49.194999853769936, 49.1983331044515, 49.20250002543131, 49.21833324432373, 49.2016666730245, 49.21791648864746, 49.20958344141642, 49.18291695912679, 49.21500015258789, 49.182082970937095, 49.27916685740153, 49.21791696548462, 49.21375020345052, 49.20833349227905, 49.21666622161865, 49.18125025431315, 49.22124973932902, 49.196666876475014, 49.17875003814697, 49.206666787465416, 49.21166658401489, 49.21458355585734, 49.22666645050049, 49.22916682561239, 49.23749987284342, 49.22208325068156, 49.22625017166138, 49.228749910990395, 49.22375011444092, 49.208750089009605, 49.22541666030884, 49.23958396911621, 49.23791694641113, 49.2333337465922, 49.22333335876465, 49.22916650772095, 49.21375020345052, 49.238749980926514, 49.2254163424174, 49.24000024795532, 49.24000024795532, 49.24708334604899, 49.23791662851969, 49.224166552225746, 49.25250005722046, 49.21500015258789, 49.204999923706055, 49.22249984741211, 49.23083337148031, 49.22083330154419, 49.21291700998942, 49.176249980926514, 49.209999879201256, 49.212916692097984, 49.21625010172526, 49.237916469573975, 49.25083351135254, 49.224583307902016, 49.22000010808309, 49.24083344141642, 49.25000015894572, 49.232083320617676, 49.26500002543131, 49.2179168065389, 49.20666710535685, 49.210833390553795, 49.19166692097982, 49.21833324432373, 49.166250228881836, 49.203333377838135, 49.21249977747599, 49.20166635513306, 49.19416650136312, 49.25208346048991, 49.24083344141642, 49.214583237965904, 49.211249669392906, 49.19541692733765, 49.204166889190674, 49.177916844685875, 49.183749516805015, 49.16583363215128, 49.18999989827474, 49.19541645050049, 49.17624966303507, 49.1670831044515, 49.18958314259847, 49.18916702270508, 49.16333278020223, 49.160416762034096, 49.145833015441895, 49.15583292643229, 49.175000031789146, 49.144583225250244, 49.129583517710365, 49.1445829073588, 49.13500006993612, 49.1141668955485, 49.14125029246012, 49.12958319981893, 49.15250031153361, 49.151249726613365, 49.0912504196167, 49.107083320617676, 49.12833340962728, 49.14583317438761, 49.14416662851969, 49.1029167175293, 49.1200000445048, 49.105417251586914, 49.08541679382324, 49.07333358128866, 49.08291673660278, 49.08541695276896, 49.073333422342934, 49.04708353678385, 49.10083373387655, 49.03833341598511, 49.04916683832804, 49.05458354949951, 49.03375005722046, 49.034583568573, 49.02208344141642, 49.02875026067098, 49.03125031789144, 49.05249993006388, 49.059999783833824, 49.049166679382324, 49.04958422978719, 49.04791673024496, 49.0220832824707, 49.04625002543131, 49.04625002543131, 49.03291670481364, 49.032500425974526, 49.00333309173584, 49.002500216166176, 49.0025003751119, 49.01958401997884, 48.96625026067098, 48.96708345413208, 48.99666659037272, 48.947083155314125, 48.95916716257731, 48.97124989827474, 48.87791681289673, 48.92749961217245, 48.981249968210854, 48.94374974568685, 48.92666641871134, 48.922500133514404, 48.93208328882853, 48.895417054494224, 48.91208346684774, 48.88416655858358, 48.89333327611288, 48.90333350499471, 48.8808331489563, 48.830416997273765, 48.860833485921226, 48.88291692733765, 48.880833307902016, 48.86666695276896, 48.86041657129923, 48.84250005086263, 48.8583337465922, 48.85666640599569, 48.82416661580404, 48.832083543141685, 48.862500031789146, 48.89375019073486, 48.86291694641113, 48.866666634877525, 48.87166659037272, 48.90416685740153, 48.8970832824707, 48.9145835240682, 48.919166564941406, 48.93625005086263, 48.8858331044515, 48.8891666730245, 48.90333318710327, 48.951666514078774, 48.944583098093666, 48.94125032424927, 49.00666650136312, 49.014166831970215, 49.002500216166176, 48.989167054494224, 48.97250016530355, 48.94583320617676, 48.97833331425985, 48.96583350499471, 48.9891668955485, 49.05833339691162, 49.02291695276896, 49.024166901906334, 48.99041700363159, 48.985833962758385, 48.99791685740153, 49.01875019073486, 48.99208354949951, 49.02749983469645, 49.03375005722046, 49.01791683832804, 49.03000020980835, 49.01541709899902, 49.04000027974447, 49.02166668574015, 49.06791687011719, 49.07541720072428, 49.049583435058594, 49.09208377202352, 49.06166728337606, 49.0691663424174, 49.10458358128866, 49.088749726613365, 49.08666626612345, 49.098750273386635, 49.08500019709269, 49.072916666666664, 49.02750015258789, 49.02500057220459, 49.067916552225746, 49.08250013987223, 49.09583361943563, 49.05000019073486, 49.05833355585734, 49.05916690826416, 49.05250024795532, 49.07041708628336, 49.04375012715658, 49.07249975204468, 49.04625002543131, 49.047500451405845, 49.03958384195963, 49.04583326975504, 49.07208379109701, 49.048333485921226, 49.06958325703939, 49.067916552225746, 49.051666577657066, 49.06375026702881, 49.03291686375936, 49.03166659673055, 49.086250146230064, 49.069166819254555, 49.07583363850912, 49.04374980926514, 49.04249986012777, 49.020417054494224, 49.05166673660278, 49.00249989827474, 49.02500009536743, 49.015833377838135, 49.044167041778564, 49.066250006357826, 49.05208349227905, 49.09250005086263, 49.02500041325887, 48.99500052134196, 48.980833212534584, 49.02250003814697, 49.0204168955485, 49.02708307902018, 49.01041682561239, 49.03041696548462, 49.02208375930786, 49.01541678110758, 49.053749879201256, 49.02250019709269, 49.040833473205566, 49.052083333333336, 49.01875019073486, 49.08416700363159, 48.983333110809326, 49.02583376566569, 49.05458370844523, 49.02916685740153, 49.03125015894572, 49.02374982833862, 49.05416711171468, 49.02583312988281, 49.02208344141642, 49.0304168065389, 49.020833333333336, 49.0254168510437, 49.03833341598511, 49.0112501780192, 49.06916666030884, 49.0112501780192, 49.065000216166176, 49.029999574025474, 49.05291668574015, 49.07291634877523, 49.00041643778483, 49.02125024795532, 49.043333212534584, 49.08333349227905, 49.0716667175293, 49.073749701182045, 49.07166703542074, 49.10166708628336, 49.07208379109701, 49.065416971842446, 49.10875002543131, 49.059583028157554, 49.09500010808309, 49.06041701634725, 49.08750009536743, 49.080416679382324, 49.08125066757202, 49.07166703542074, 49.09000047047933, 49.06666692097982, 49.110416889190674, 49.065416971842446, 49.07833353678385, 49.13291676839193, 49.08125034968058, 49.08041683832804, 49.08458344141642, 49.0516668955485, 49.08458375930786, 49.070416609446205, 49.09458351135254, 49.07333358128866, 49.05875031153361, 49.106250286102295, 49.09416707356771, 49.05000066757202, 49.093333880106606, 49.09375015894572, 49.09999958674113, 49.081250031789146, 49.140833059946694, 49.07708326975504, 49.0600004196167, 49.09083334604899, 49.09416691462199, 49.06458361943563, 49.15125052134196, 49.12000020345052, 49.10541693369547, 49.11041673024496, 49.08750025431315, 49.13541682561239, 49.11500024795532, 49.0854172706604, 49.09750016530355, 49.1200000445048, 49.110416889190674, 49.12083355585734, 49.120833237965904, 49.10625044504801, 49.09708340962728, 49.15208339691162, 49.13583326339722, 49.102916876475014, 49.13583326339722, 49.12083339691162, 49.13958342870077, 49.15666659673055, 49.13833347956339, 49.119166692097984, 49.14458338419596, 49.148750146230064, 49.12208334604899, 49.11458365122477, 49.11208359400431, 49.18458350499471, 49.19249947865804, 49.15291659037272, 49.17208274205526, 49.15499989191691, 49.16499980290731, 49.15499989191691, 49.18708308537801, 49.184583028157554, 49.14666668574015, 49.14749972025553, 49.180000146230064, 49.1766668955485, 49.17458311716715, 49.20458300908407, 49.15916681289673, 49.183333237965904, 49.19583304723104, 49.15999984741211, 49.20458300908407, 49.168749968210854, 49.18749968210856, 49.150416692097984, 49.17208321889242, 49.21333312988281, 49.175833543141685, 49.18708340326945, 49.178749879201256, 49.200416564941406, 49.19624996185303, 49.21708313624064, 49.16958316167196, 49.169583002726235, 49.146666844685875, 49.18958346048991, 49.19750006993612, 49.202499866485596, 49.181666692097984, 49.19833326339722, 49.18458318710327, 49.18249988555908, 49.19166612625122, 49.20791673660278, 49.199166456858315, 49.20124991734823, 49.21083323160807, 49.207916259765625, 49.193750063578285, 49.22208340962728, 49.212916692097984, 49.197499910990395, 49.192917029062905, 49.20208342870077, 49.19083309173584, 49.21083307266235, 49.198333422342934, 49.228333473205566, 49.18666696548462, 49.201666831970215, 49.21249977747599, 49.21249977747599, 49.22166665395101, 49.227083365122475, 49.21500031153361, 49.19041649500529, 49.23458353678385, 49.234166940053306, 49.19125016530355, 49.20291678110758, 49.191666762034096, 49.20874993006388, 49.23125012715658, 49.19500001271566, 49.168333530426025, 49.193750063578285, 49.23499997456869, 49.18291695912679, 49.17583306630453, 49.169999758402504, 49.14916674296061, 49.184999783833824, 49.16083319981893, 49.14416662851969, 49.17708349227905, 49.16666634877523, 49.16958347956339, 49.184166272481285, 49.148749669392906, 49.15333318710327, 49.20374997456869, 49.13250001271566, 49.11041673024496, 49.142083168029785, 49.13541650772095, 49.112500031789146, 49.12624994913737, 49.11958360671997, 49.141250133514404, 49.11791674296061, 49.11833381652832, 49.097083727518715, 49.088333447774254, 49.10875050226847, 49.12666686375936, 49.098333517710365, 49.07250054677328, 49.096666971842446, 49.1029167175293, 49.08416716257731, 49.0533332824707, 49.06500005722046, 49.05416695276896, 49.0420831044515, 49.071666876475014, 49.017500241597496, 49.08833312988281, 49.021250089009605, 49.027916431427, 49.035833517710365, 49.04083363215128, 49.07750018437704, 49.01874987284342, 48.984166940053306, 49.003750006357826, 49.01291688283285, 49.01375039418539, 49.040000120798744, 49.00874996185303, 49.048750241597496, 49.05875015258789, 49.072916984558105, 49.036250273386635, 48.99791685740153, 48.990000089009605, 49.003333568573, 48.96916675567627, 48.97083361943563, 48.923749923706055, 48.99541680018107, 48.961250146230064, 48.93374967575073, 48.930416425069176, 48.98791694641113, 49.00583346684774, 48.90791654586792, 48.92958370844523, 48.9054168065389, 48.980417251586914, 48.94500017166138, 48.95291678110758, 48.93291632334391, 48.956250031789146, 48.93166653315226, 48.91333341598511, 48.934999783833824, 48.93874994913737, 48.93583345413208, 48.91083383560181, 48.97625001271566, 48.985833168029785, 48.93916686375936, 48.92833375930786, 48.93916654586792, 48.93583313624064, 48.94416666030884, 48.93416674931844, 48.93833335240682, 48.94708363215128, 48.9266668955485, 48.947083473205566, 48.949999968210854, 48.944999853769936, 48.92625013987223, 48.93125057220459, 48.96250041325887, 48.97458346684774, 48.950000286102295, 48.9629168510437, 49.00166686375936, 48.96666653951009, 48.95583327611288, 48.96583350499471, 49.0237504641215, 49.038333574930824, 49.05708360671997, 49.11625019709269, 49.09041674931844, 49.10166629155477, 49.105000019073486, 49.08958355585734, 49.07583347956339, 49.0824998219808, 49.09375, 49.06708319981893, 49.06375010808309, 49.0845832824707, 49.07250006993612, 49.079999923706055, 49.02916701634725, 49.047083695729576, 49.0479162534078, 49.052916526794434, 49.05541706085205, 49.058749993642174, 49.00416660308838, 49.01291688283285, 49.06375010808309, 49.02000013987223, 49.03833373387655, 49.05958334604899, 49.06208340326945, 49.05708376566569, 49.048333485921226, 49.03041664759318, 49.01583353678385, 49.015833377838135, 49.04083379109701, 49.04583342870077, 49.01874987284342, 49.04625050226847, 49.072499910990395, 49.02875010172526, 49.080833752950035, 49.08624982833862, 49.07833353678385, 49.012499968210854, 49.04541699091593, 48.976666609446205, 49.047500451405845, 49.00124994913737, 49.002500216166176, 49.03791666030884, 49.038333574930824, 49.037083307902016, 49.01875019073486, 49.0025003751119, 48.977500438690186, 48.925833543141685, 48.987499713897705, 48.952916622161865, 48.9983336130778, 49.01000038782755, 48.994166692097984, 49.00625022252401, 48.984583695729576, 49.02083365122477, 48.990416844685875, 48.98958349227905, 48.95666662851969, 48.961250146230064, 48.97625080744425, 49.015416940053306, 48.99791685740153, 49.018333752950035, 49.0962503751119, 48.97499990463257, 48.98666683832804, 49.02291695276896, 49.02083285649618, 49.011666456858315, 49.0141666730245, 49.045416831970215, 49.03500032424927, 49.043333530426025, 49.0008331934611, 49.01583353678385, 49.022916634877525, 49.04083283742269, 49.035000006357826, 49.0245836575826, 49.01083358128866, 49.017083168029785, 48.99750026067098, 49.02708339691162, 49.02125024795532, 48.98375050226847, 49.0112501780192, 48.99416716893514, 48.99375025431315, 48.981667041778564, 48.974167029062905, 48.95791641871134, 48.97999986012777, 48.94291671117147, 48.98750019073486, 48.99375009536743, 48.99208323160807, 48.97249984741211, 49.00333388646444, 49.005833784739174, 48.99541680018107, 49.03291686375936, 49.040833473205566, 49.0166662534078, 48.99958372116089, 48.980833212534584, 49.007500648498535, 49.034583727518715, 49.03458340962728, 49.05625009536743, 49.02916653951009, 49.078750451405845, 49.03500016530355, 49.08000008265177, 49.03916645050049, 49.09583346048991, 49.076666514078774, 49.09041674931844, 49.078750133514404, 49.100000063578285, 49.12166690826416, 49.050416787465416, 49.04458363850912, 49.07458337148031, 49.103749910990395, 49.09083398183187, 49.0820837020874, 49.10166692733765, 49.06166664759318, 49.100000063578285, 49.079999923706055, 49.0674999554952, 49.06291707356771, 49.072083473205566, 49.119166692097984, 49.070000330607094, 49.09374968210856, 49.07666635513306, 49.09083318710327, 49.083333810170494, 49.10791699091593, 49.08958355585734, 49.03791697820028, 49.08333365122477, 49.0766666730245, 49.1020835240682, 49.03000036875407, 49.107083320617676, 49.08125019073486, 49.10625012715658, 49.078750451405845, 49.07250006993612, 49.10416682561239, 49.13000011444092, 49.09625005722046, 49.12125015258789, 49.139583110809326, 49.130833784739174, 49.09166669845581, 49.11583344141642, 49.11750030517578, 49.13833316167196, 49.106667041778564, 49.115416844685875, 49.10750023523966, 49.11250019073486, 49.1191668510437, 49.13083346684774, 49.09333372116089, 49.11291694641113, 49.12291685740153, 49.106666564941406, 49.13458363215128, 49.12125031153361, 49.171250343322754, 49.19083340962728, 49.224166552225746, 49.180416425069176, 49.160416762034096, 49.16416676839193, 49.17625013987223, 49.19458278020223, 49.14874998728434, 49.147916634877525, 49.10583337148031, 49.179166634877525, 49.21083307266235, 49.161667029062905, 49.19166660308838, 49.171250343322754, 49.21625026067098, 49.159583727518715, 49.19791634877523, 49.1554168065389, 49.184999783833824, 49.18874963124593, 49.19333283106486, 49.1987501780192, 49.162083307902016, 49.168750286102295, 49.172083059946694, 49.1704166730245, 49.16833368937174, 49.174166997273765, 49.1924999554952, 49.14291652043661, 49.15458329518636, 49.167499701182045, 49.18625005086263, 49.19416666030884, 49.151666482289635, 49.224583307902016, 49.23583364486694, 49.172083695729576, 49.172083377838135, 49.2270835240682, 49.21916659673055, 49.20666662851969, 49.19166692097982, 49.17416683832804, 49.199583530426025, 49.15874989827474, 49.272916634877525, 49.21666685740153, 49.20583311716715, 49.171666622161865, 49.225000063578285, 49.18583329518636, 49.14875030517578, 49.208749771118164, 49.21333360671997, 49.24041668574015, 49.19541708628336, 49.23541673024496, 49.21374988555908, 49.232499758402504, 49.17750056584676, 49.193750063578285, 49.25458335876465, 49.196666876475014, 49.18374983469645, 49.204166889190674, 49.2383337020874, 49.17999998728434, 49.20208342870077, 49.209166526794434, 49.234166622161865, 49.19958368937174, 49.20583327611288, 49.20000012715658, 49.178750356038414, 49.18416674931844, 49.2041662534078, 49.207500298817955, 49.183333237965904, 49.18499994277954, 49.23708311716715, 49.15208307902018, 49.18583297729492, 49.197916666666664, 49.18791659673055, 49.161250273386635, 49.12000020345052, 49.184166431427, 49.142083168029785, 49.162916819254555, 49.18291632334391, 49.25291633605957, 49.174582958221436, 49.15583308537801, 49.120833237965904, 49.132916609446205, 49.16125011444092, 49.15625, 49.1791664759318, 49.150416215260826, 49.13499975204468, 49.194583098093666, 49.15708351135254, 49.16124979654948, 49.139166514078774, 49.12291638056437, 49.142082850138344, 49.105833530426025, 49.12708298365275, 49.159583568573, 49.145416577657066, 49.140000343322754, 49.13958326975504, 49.1200000445048, 49.165833155314125, 49.10541661580404, 49.08875020345052, 49.14750003814697, 49.10125017166138, 49.084583600362144, 49.0929168065389, 49.1079166730245, 49.14833323160807, 49.080416679382324, 49.119583447774254, 49.08833360671997, 49.12958319981893, 49.09499994913737, 49.10124969482422, 49.06916602452596, 49.0929168065389, 49.08624998728434, 49.083333333333336, 49.08708302179972, 49.07541720072428, 49.054166316986084, 49.09500010808309, 49.03083324432373, 49.02250019709269, 49.04000027974447, 49.056249936421715, 49.0404167175293, 49.04166714350382, 49.0429162979126, 49.107500076293945, 48.97666613260905, 49.09375, 49.06333351135254, 49.05333296457926, 49.011666774749756, 49.052499771118164, 49.06666692097982, 49.05666700998942, 49.05041662851969, 49.07250006993612, 49.089583237965904, 49.083333333333336], "yaxis": "y"}], "layout": {"legend": {"title": {"text": "variable"}, "tracegroupgap": 0}, "margin": {"t": 60}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "index"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "value"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["df1.loc[df1['ITEMID']==3211, 'OBSVALUE'].resample('1d').mean().plot()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Work.localized/Python/resistivity_2023/res_remove/resistivity_avam_2022.py:144: FutureWarning:\n", "\n", "The default value of numeric_only in DataFrameGroupBy.median is deprecated. In a future version, numeric_only will default to False. Either specify numeric_only or select only columns which should be valid for the function.\n", "\n", "/Users/<USER>/opt/miniconda3/lib/python3.8/site-packages/pywt/_multilevel.py:43: UserWarning:\n", "\n", "Level value of 10 is too high: all coefficients will experience boundary effects.\n", "\n", "/Users/<USER>/Work.localized/Python/resistivity_2023/res_remove/resistivity_avam_2022.py:298: SettingWithCopyWarning:\n", "\n", "\n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "\n", "/Users/<USER>/Work.localized/Python/resistivity_2023/res_remove/resistivity_avam_2022.py:299: SettingWithCopyWarning:\n", "\n", "\n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "\n"]}], "source": ["fs = ra.fourier_smoothing()\n", "df = fs.fourier_removing(df1)\n", "wt = ra.wavelets_trend()\n", "df = wt.wavelets_dec(df)\n", "rr = ra.relative_range()\n", "df = rr.calculate_relative_range(df)\n", "aa = ra.annual_amplitude()\n", "df = aa.res_hilbert(df)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>OBSVALUE</th>\n", "      <th>fourier</th>\n", "      <th>residual</th>\n", "      <th>trend</th>\n", "      <th>new_residual</th>\n", "      <th>flag</th>\n", "      <th>start</th>\n", "      <th>relative_range</th>\n", "      <th>hilbert</th>\n", "      <th>annual_1</th>\n", "      <th>annual_2</th>\n", "      <th>std_1</th>\n", "      <th>std_2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2018-01-01 00:00:00+00:00</th>\n", "      <td>49.490005</td>\n", "      <td>0.067237</td>\n", "      <td>49.422769</td>\n", "      <td>49.426013</td>\n", "      <td>49.431984</td>\n", "      <td>True</td>\n", "      <td>49.422769</td>\n", "      <td>0.000000</td>\n", "      <td>0.251559</td>\n", "      <td>0.125778</td>\n", "      <td>-0.125778</td>\n", "      <td>0.805003</td>\n", "      <td>-0.805003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-02 00:00:00+00:00</th>\n", "      <td>49.490005</td>\n", "      <td>0.067746</td>\n", "      <td>49.422259</td>\n", "      <td>49.425842</td>\n", "      <td>49.432334</td>\n", "      <td>False</td>\n", "      <td>49.422769</td>\n", "      <td>-0.001030</td>\n", "      <td>0.160530</td>\n", "      <td>0.125778</td>\n", "      <td>-0.125778</td>\n", "      <td>0.805003</td>\n", "      <td>-0.805003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-03 00:00:00+00:00</th>\n", "      <td>49.480005</td>\n", "      <td>0.068235</td>\n", "      <td>49.411770</td>\n", "      <td>49.425671</td>\n", "      <td>49.432717</td>\n", "      <td>False</td>\n", "      <td>49.422769</td>\n", "      <td>-0.022254</td>\n", "      <td>0.161578</td>\n", "      <td>0.125778</td>\n", "      <td>-0.125778</td>\n", "      <td>0.805003</td>\n", "      <td>-0.805003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-04 00:00:00+00:00</th>\n", "      <td>49.470005</td>\n", "      <td>0.068814</td>\n", "      <td>49.401191</td>\n", "      <td>49.425500</td>\n", "      <td>49.433136</td>\n", "      <td>False</td>\n", "      <td>49.422769</td>\n", "      <td>-0.043659</td>\n", "      <td>0.133589</td>\n", "      <td>0.125778</td>\n", "      <td>-0.125778</td>\n", "      <td>0.805003</td>\n", "      <td>-0.805003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-05 00:00:00+00:00</th>\n", "      <td>49.490005</td>\n", "      <td>0.069427</td>\n", "      <td>49.420579</td>\n", "      <td>49.425329</td>\n", "      <td>49.433596</td>\n", "      <td>False</td>\n", "      <td>49.422769</td>\n", "      <td>-0.004431</td>\n", "      <td>0.134338</td>\n", "      <td>0.125778</td>\n", "      <td>-0.125778</td>\n", "      <td>0.805003</td>\n", "      <td>-0.805003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-26 00:00:00+00:00</th>\n", "      <td>49.059999</td>\n", "      <td>-0.083833</td>\n", "      <td>49.143833</td>\n", "      <td>49.119477</td>\n", "      <td>49.151398</td>\n", "      <td>False</td>\n", "      <td>49.093235</td>\n", "      <td>0.103065</td>\n", "      <td>0.134482</td>\n", "      <td>0.099568</td>\n", "      <td>-0.099568</td>\n", "      <td>0.805003</td>\n", "      <td>-0.805003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-27 00:00:00+00:00</th>\n", "      <td>49.049999</td>\n", "      <td>-0.084752</td>\n", "      <td>49.134751</td>\n", "      <td>49.119545</td>\n", "      <td>49.152363</td>\n", "      <td>False</td>\n", "      <td>49.093235</td>\n", "      <td>0.084566</td>\n", "      <td>0.135667</td>\n", "      <td>0.099568</td>\n", "      <td>-0.099568</td>\n", "      <td>0.805003</td>\n", "      <td>-0.805003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-28 00:00:00+00:00</th>\n", "      <td>49.059999</td>\n", "      <td>-0.085744</td>\n", "      <td>49.145743</td>\n", "      <td>49.119613</td>\n", "      <td>49.153291</td>\n", "      <td>False</td>\n", "      <td>49.093235</td>\n", "      <td>0.106956</td>\n", "      <td>0.164123</td>\n", "      <td>0.099568</td>\n", "      <td>-0.099568</td>\n", "      <td>0.805003</td>\n", "      <td>-0.805003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-29 00:00:00+00:00</th>\n", "      <td>49.120001</td>\n", "      <td>-0.086207</td>\n", "      <td>49.206207</td>\n", "      <td>49.119682</td>\n", "      <td>49.154178</td>\n", "      <td>False</td>\n", "      <td>49.093235</td>\n", "      <td>0.230118</td>\n", "      <td>0.165441</td>\n", "      <td>0.099568</td>\n", "      <td>-0.099568</td>\n", "      <td>0.805003</td>\n", "      <td>-0.805003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-30 00:00:00+00:00</th>\n", "      <td>49.070002</td>\n", "      <td>-0.086852</td>\n", "      <td>49.156854</td>\n", "      <td>49.119751</td>\n", "      <td>49.155025</td>\n", "      <td>False</td>\n", "      <td>49.093235</td>\n", "      <td>0.129588</td>\n", "      <td>0.255850</td>\n", "      <td>0.099568</td>\n", "      <td>-0.099568</td>\n", "      <td>0.805003</td>\n", "      <td>-0.805003</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1976 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                            OBSVALUE   fourier   residual      trend  \\\n", "2018-01-01 00:00:00+00:00  49.490005  0.067237  49.422769  49.426013   \n", "2018-01-02 00:00:00+00:00  49.490005  0.067746  49.422259  49.425842   \n", "2018-01-03 00:00:00+00:00  49.480005  0.068235  49.411770  49.425671   \n", "2018-01-04 00:00:00+00:00  49.470005  0.068814  49.401191  49.425500   \n", "2018-01-05 00:00:00+00:00  49.490005  0.069427  49.420579  49.425329   \n", "...                              ...       ...        ...        ...   \n", "2023-05-26 00:00:00+00:00  49.059999 -0.083833  49.143833  49.119477   \n", "2023-05-27 00:00:00+00:00  49.049999 -0.084752  49.134751  49.119545   \n", "2023-05-28 00:00:00+00:00  49.059999 -0.085744  49.145743  49.119613   \n", "2023-05-29 00:00:00+00:00  49.120001 -0.086207  49.206207  49.119682   \n", "2023-05-30 00:00:00+00:00  49.070002 -0.086852  49.156854  49.119751   \n", "\n", "                           new_residual   flag      start  relative_range  \\\n", "2018-01-01 00:00:00+00:00     49.431984   True  49.422769        0.000000   \n", "2018-01-02 00:00:00+00:00     49.432334  False  49.422769       -0.001030   \n", "2018-01-03 00:00:00+00:00     49.432717  False  49.422769       -0.022254   \n", "2018-01-04 00:00:00+00:00     49.433136  False  49.422769       -0.043659   \n", "2018-01-05 00:00:00+00:00     49.433596  False  49.422769       -0.004431   \n", "...                                 ...    ...        ...             ...   \n", "2023-05-26 00:00:00+00:00     49.151398  False  49.093235        0.103065   \n", "2023-05-27 00:00:00+00:00     49.152363  False  49.093235        0.084566   \n", "2023-05-28 00:00:00+00:00     49.153291  False  49.093235        0.106956   \n", "2023-05-29 00:00:00+00:00     49.154178  False  49.093235        0.230118   \n", "2023-05-30 00:00:00+00:00     49.155025  False  49.093235        0.129588   \n", "\n", "                            hilbert  annual_1  annual_2     std_1     std_2  \n", "2018-01-01 00:00:00+00:00  0.251559  0.125778 -0.125778  0.805003 -0.805003  \n", "2018-01-02 00:00:00+00:00  0.160530  0.125778 -0.125778  0.805003 -0.805003  \n", "2018-01-03 00:00:00+00:00  0.161578  0.125778 -0.125778  0.805003 -0.805003  \n", "2018-01-04 00:00:00+00:00  0.133589  0.125778 -0.125778  0.805003 -0.805003  \n", "2018-01-05 00:00:00+00:00  0.134338  0.125778 -0.125778  0.805003 -0.805003  \n", "...                             ...       ...       ...       ...       ...  \n", "2023-05-26 00:00:00+00:00  0.134482  0.099568 -0.099568  0.805003 -0.805003  \n", "2023-05-27 00:00:00+00:00  0.135667  0.099568 -0.099568  0.805003 -0.805003  \n", "2023-05-28 00:00:00+00:00  0.164123  0.099568 -0.099568  0.805003 -0.805003  \n", "2023-05-29 00:00:00+00:00  0.165441  0.099568 -0.099568  0.805003 -0.805003  \n", "2023-05-30 00:00:00+00:00  0.255850  0.099568 -0.099568  0.805003 -0.805003  \n", "\n", "[1976 rows x 13 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "variable=relative_range<br>index=%{x}<br>value=%{y}<extra></extra>", "legendgroup": "relative_range", "line": {"color": "#636efa", "dash": "solid"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "relative_range", "showlegend": true, "type": "scattergl", "x": ["2018-01-01T00:00:00+00:00", "2018-01-02T00:00:00+00:00", "2018-01-03T00:00:00+00:00", "2018-01-04T00:00:00+00:00", "2018-01-05T00:00:00+00:00", "2018-01-06T00:00:00+00:00", "2018-01-07T00:00:00+00:00", "2018-01-08T00:00:00+00:00", "2018-01-09T00:00:00+00:00", "2018-01-10T00:00:00+00:00", "2018-01-11T00:00:00+00:00", "2018-01-12T00:00:00+00:00", "2018-01-13T00:00:00+00:00", "2018-01-14T00:00:00+00:00", "2018-01-15T00:00:00+00:00", "2018-01-16T00:00:00+00:00", "2018-01-17T00:00:00+00:00", "2018-01-18T00:00:00+00:00", "2018-01-19T00:00:00+00:00", "2018-01-20T00:00:00+00:00", "2018-01-21T00:00:00+00:00", "2018-01-22T00:00:00+00:00", "2018-01-23T00:00:00+00:00", "2018-01-24T00:00:00+00:00", "2018-01-25T00:00:00+00:00", "2018-01-26T00:00:00+00:00", "2018-01-27T00:00:00+00:00", "2018-01-28T00:00:00+00:00", "2018-01-29T00:00:00+00:00", "2018-01-30T00:00:00+00:00", "2018-01-31T00:00:00+00:00", "2018-02-01T00:00:00+00:00", "2018-02-02T00:00:00+00:00", "2018-02-03T00:00:00+00:00", "2018-02-04T00:00:00+00:00", "2018-02-05T00:00:00+00:00", "2018-02-06T00:00:00+00:00", "2018-02-07T00:00:00+00:00", "2018-02-08T00:00:00+00:00", "2018-02-09T00:00:00+00:00", "2018-02-10T00:00:00+00:00", "2018-02-11T00:00:00+00:00", "2018-02-12T00:00:00+00:00", "2018-02-13T00:00:00+00:00", "2018-02-14T00:00:00+00:00", "2018-02-15T00:00:00+00:00", "2018-02-16T00:00:00+00:00", "2018-02-17T00:00:00+00:00", "2018-02-18T00:00:00+00:00", "2018-02-19T00:00:00+00:00", "2018-02-20T00:00:00+00:00", "2018-02-21T00:00:00+00:00", "2018-02-22T00:00:00+00:00", "2018-02-23T00:00:00+00:00", "2018-02-24T00:00:00+00:00", "2018-02-25T00:00:00+00:00", "2018-02-26T00:00:00+00:00", "2018-02-27T00:00:00+00:00", "2018-02-28T00:00:00+00:00", "2018-03-01T00:00:00+00:00", "2018-03-02T00:00:00+00:00", "2018-03-03T00:00:00+00:00", "2018-03-04T00:00:00+00:00", "2018-03-05T00:00:00+00:00", "2018-03-06T00:00:00+00:00", "2018-03-07T00:00:00+00:00", "2018-03-08T00:00:00+00:00", "2018-03-09T00:00:00+00:00", "2018-03-10T00:00:00+00:00", "2018-03-11T00:00:00+00:00", "2018-03-12T00:00:00+00:00", "2018-03-13T00:00:00+00:00", "2018-03-14T00:00:00+00:00", "2018-03-15T00:00:00+00:00", "2018-03-16T00:00:00+00:00", "2018-03-17T00:00:00+00:00", "2018-03-18T00:00:00+00:00", "2018-03-19T00:00:00+00:00", "2018-03-20T00:00:00+00:00", "2018-03-21T00:00:00+00:00", "2018-03-22T00:00:00+00:00", "2018-03-23T00:00:00+00:00", "2018-03-24T00:00:00+00:00", "2018-03-25T00:00:00+00:00", "2018-03-26T00:00:00+00:00", "2018-03-27T00:00:00+00:00", "2018-03-28T00:00:00+00:00", "2018-03-29T00:00:00+00:00", "2018-03-30T00:00:00+00:00", "2018-03-31T00:00:00+00:00", "2018-04-01T00:00:00+00:00", "2018-04-02T00:00:00+00:00", "2018-04-03T00:00:00+00:00", "2018-04-04T00:00:00+00:00", "2018-04-05T00:00:00+00:00", "2018-04-06T00:00:00+00:00", "2018-04-07T00:00:00+00:00", "2018-04-08T00:00:00+00:00", "2018-04-09T00:00:00+00:00", "2018-04-10T00:00:00+00:00", "2018-04-11T00:00:00+00:00", "2018-04-12T00:00:00+00:00", "2018-04-13T00:00:00+00:00", "2018-04-14T00:00:00+00:00", "2018-04-15T00:00:00+00:00", "2018-04-16T00:00:00+00:00", "2018-04-17T00:00:00+00:00", "2018-04-18T00:00:00+00:00", "2018-04-19T00:00:00+00:00", "2018-04-20T00:00:00+00:00", "2018-04-21T00:00:00+00:00", "2018-04-22T00:00:00+00:00", "2018-04-23T00:00:00+00:00", "2018-04-24T00:00:00+00:00", "2018-04-25T00:00:00+00:00", "2018-04-26T00:00:00+00:00", "2018-04-27T00:00:00+00:00", "2018-04-28T00:00:00+00:00", "2018-04-29T00:00:00+00:00", "2018-04-30T00:00:00+00:00", "2018-05-01T00:00:00+00:00", "2018-05-02T00:00:00+00:00", "2018-05-03T00:00:00+00:00", "2018-05-04T00:00:00+00:00", "2018-05-05T00:00:00+00:00", "2018-05-06T00:00:00+00:00", "2018-05-07T00:00:00+00:00", "2018-05-08T00:00:00+00:00", "2018-05-09T00:00:00+00:00", "2018-05-10T00:00:00+00:00", "2018-05-11T00:00:00+00:00", "2018-05-12T00:00:00+00:00", "2018-05-13T00:00:00+00:00", "2018-05-14T00:00:00+00:00", "2018-05-15T00:00:00+00:00", "2018-05-16T00:00:00+00:00", "2018-05-17T00:00:00+00:00", "2018-05-18T00:00:00+00:00", "2018-05-19T00:00:00+00:00", "2018-05-20T00:00:00+00:00", "2018-05-21T00:00:00+00:00", "2018-05-22T00:00:00+00:00", "2018-05-23T00:00:00+00:00", "2018-05-24T00:00:00+00:00", "2018-05-25T00:00:00+00:00", "2018-05-26T00:00:00+00:00", "2018-05-27T00:00:00+00:00", "2018-05-28T00:00:00+00:00", "2018-05-29T00:00:00+00:00", "2018-05-30T00:00:00+00:00", "2018-05-31T00:00:00+00:00", "2018-06-01T00:00:00+00:00", "2018-06-02T00:00:00+00:00", "2018-06-03T00:00:00+00:00", "2018-06-04T00:00:00+00:00", "2018-06-05T00:00:00+00:00", "2018-06-06T00:00:00+00:00", "2018-06-07T00:00:00+00:00", "2018-06-08T00:00:00+00:00", "2018-06-09T00:00:00+00:00", "2018-06-10T00:00:00+00:00", "2018-06-11T00:00:00+00:00", "2018-06-12T00:00:00+00:00", "2018-06-13T00:00:00+00:00", "2018-06-14T00:00:00+00:00", "2018-06-15T00:00:00+00:00", "2018-06-16T00:00:00+00:00", "2018-06-17T00:00:00+00:00", "2018-06-18T00:00:00+00:00", "2018-06-19T00:00:00+00:00", "2018-06-20T00:00:00+00:00", "2018-06-21T00:00:00+00:00", "2018-06-22T00:00:00+00:00", "2018-06-23T00:00:00+00:00", "2018-06-24T00:00:00+00:00", "2018-06-25T00:00:00+00:00", "2018-06-26T00:00:00+00:00", "2018-06-27T00:00:00+00:00", "2018-06-28T00:00:00+00:00", "2018-06-29T00:00:00+00:00", "2018-06-30T00:00:00+00:00", "2018-07-01T00:00:00+00:00", "2018-07-02T00:00:00+00:00", "2018-07-03T00:00:00+00:00", "2018-07-04T00:00:00+00:00", "2018-07-05T00:00:00+00:00", "2018-07-06T00:00:00+00:00", "2018-07-07T00:00:00+00:00", "2018-07-08T00:00:00+00:00", "2018-07-09T00:00:00+00:00", "2018-07-10T00:00:00+00:00", "2018-07-11T00:00:00+00:00", "2018-07-12T00:00:00+00:00", "2018-07-13T00:00:00+00:00", "2018-07-14T00:00:00+00:00", "2018-07-15T00:00:00+00:00", "2018-07-16T00:00:00+00:00", "2018-07-17T00:00:00+00:00", "2018-07-18T00:00:00+00:00", "2018-07-19T00:00:00+00:00", "2018-07-20T00:00:00+00:00", "2018-07-21T00:00:00+00:00", "2018-07-22T00:00:00+00:00", "2018-07-23T00:00:00+00:00", "2018-07-24T00:00:00+00:00", "2018-07-25T00:00:00+00:00", "2018-07-26T00:00:00+00:00", "2018-07-27T00:00:00+00:00", "2018-07-28T00:00:00+00:00", "2018-07-29T00:00:00+00:00", "2018-07-30T00:00:00+00:00", "2018-07-31T00:00:00+00:00", "2018-08-01T00:00:00+00:00", "2018-08-02T00:00:00+00:00", "2018-08-03T00:00:00+00:00", "2018-08-04T00:00:00+00:00", "2018-08-05T00:00:00+00:00", "2018-08-06T00:00:00+00:00", "2018-08-07T00:00:00+00:00", "2018-08-08T00:00:00+00:00", "2018-08-09T00:00:00+00:00", "2018-08-10T00:00:00+00:00", "2018-08-11T00:00:00+00:00", "2018-08-12T00:00:00+00:00", "2018-08-13T00:00:00+00:00", "2018-08-14T00:00:00+00:00", "2018-08-15T00:00:00+00:00", "2018-08-16T00:00:00+00:00", "2018-08-17T00:00:00+00:00", "2018-08-18T00:00:00+00:00", "2018-08-19T00:00:00+00:00", "2018-08-20T00:00:00+00:00", "2018-08-21T00:00:00+00:00", "2018-08-22T00:00:00+00:00", "2018-08-23T00:00:00+00:00", "2018-08-24T00:00:00+00:00", "2018-08-25T00:00:00+00:00", "2018-08-26T00:00:00+00:00", "2018-08-27T00:00:00+00:00", "2018-08-28T00:00:00+00:00", "2018-08-29T00:00:00+00:00", "2018-08-30T00:00:00+00:00", "2018-08-31T00:00:00+00:00", "2018-09-01T00:00:00+00:00", "2018-09-02T00:00:00+00:00", "2018-09-03T00:00:00+00:00", "2018-09-04T00:00:00+00:00", "2018-09-05T00:00:00+00:00", "2018-09-06T00:00:00+00:00", "2018-09-07T00:00:00+00:00", "2018-09-08T00:00:00+00:00", "2018-09-09T00:00:00+00:00", "2018-09-10T00:00:00+00:00", "2018-09-11T00:00:00+00:00", "2018-09-12T00:00:00+00:00", "2018-09-13T00:00:00+00:00", "2018-09-14T00:00:00+00:00", "2018-09-15T00:00:00+00:00", "2018-09-16T00:00:00+00:00", "2018-09-17T00:00:00+00:00", "2018-09-18T00:00:00+00:00", "2018-09-19T00:00:00+00:00", "2018-09-20T00:00:00+00:00", "2018-09-21T00:00:00+00:00", "2018-09-22T00:00:00+00:00", "2018-09-23T00:00:00+00:00", "2018-09-24T00:00:00+00:00", "2018-09-25T00:00:00+00:00", "2018-09-26T00:00:00+00:00", "2018-09-27T00:00:00+00:00", "2018-09-28T00:00:00+00:00", "2018-09-29T00:00:00+00:00", "2018-09-30T00:00:00+00:00", "2018-10-01T00:00:00+00:00", "2018-10-02T00:00:00+00:00", "2018-10-03T00:00:00+00:00", "2018-10-04T00:00:00+00:00", "2018-10-05T00:00:00+00:00", "2018-10-06T00:00:00+00:00", "2018-10-07T00:00:00+00:00", "2018-10-08T00:00:00+00:00", "2018-10-09T00:00:00+00:00", "2018-10-10T00:00:00+00:00", "2018-10-11T00:00:00+00:00", "2018-10-12T00:00:00+00:00", "2018-10-13T00:00:00+00:00", "2018-10-14T00:00:00+00:00", "2018-10-15T00:00:00+00:00", "2018-10-16T00:00:00+00:00", "2018-10-17T00:00:00+00:00", "2018-10-18T00:00:00+00:00", "2018-10-19T00:00:00+00:00", "2018-10-20T00:00:00+00:00", "2018-10-21T00:00:00+00:00", "2018-10-22T00:00:00+00:00", "2018-10-23T00:00:00+00:00", "2018-10-24T00:00:00+00:00", "2018-10-25T00:00:00+00:00", "2018-10-26T00:00:00+00:00", "2018-10-27T00:00:00+00:00", "2018-10-28T00:00:00+00:00", "2018-10-29T00:00:00+00:00", "2018-10-30T00:00:00+00:00", "2018-10-31T00:00:00+00:00", "2018-11-01T00:00:00+00:00", "2018-11-02T00:00:00+00:00", "2018-11-03T00:00:00+00:00", "2018-11-04T00:00:00+00:00", "2018-11-05T00:00:00+00:00", "2018-11-06T00:00:00+00:00", "2018-11-07T00:00:00+00:00", "2018-11-08T00:00:00+00:00", "2018-11-09T00:00:00+00:00", "2018-11-10T00:00:00+00:00", "2018-11-11T00:00:00+00:00", "2018-11-12T00:00:00+00:00", "2018-11-13T00:00:00+00:00", "2018-11-14T00:00:00+00:00", "2018-11-15T00:00:00+00:00", "2018-11-16T00:00:00+00:00", "2018-11-17T00:00:00+00:00", "2018-11-18T00:00:00+00:00", "2018-11-19T00:00:00+00:00", "2018-11-20T00:00:00+00:00", "2018-11-21T00:00:00+00:00", "2018-11-22T00:00:00+00:00", "2018-11-23T00:00:00+00:00", "2018-11-24T00:00:00+00:00", "2018-11-25T00:00:00+00:00", "2018-11-26T00:00:00+00:00", "2018-11-27T00:00:00+00:00", "2018-11-28T00:00:00+00:00", "2018-11-29T00:00:00+00:00", "2018-11-30T00:00:00+00:00", "2018-12-01T00:00:00+00:00", "2018-12-02T00:00:00+00:00", "2018-12-03T00:00:00+00:00", "2018-12-04T00:00:00+00:00", "2018-12-05T00:00:00+00:00", "2018-12-06T00:00:00+00:00", "2018-12-07T00:00:00+00:00", "2018-12-08T00:00:00+00:00", "2018-12-09T00:00:00+00:00", "2018-12-10T00:00:00+00:00", "2018-12-11T00:00:00+00:00", "2018-12-12T00:00:00+00:00", "2018-12-13T00:00:00+00:00", "2018-12-14T00:00:00+00:00", "2018-12-15T00:00:00+00:00", "2018-12-16T00:00:00+00:00", "2018-12-17T00:00:00+00:00", "2018-12-18T00:00:00+00:00", "2018-12-19T00:00:00+00:00", "2018-12-20T00:00:00+00:00", "2018-12-21T00:00:00+00:00", "2018-12-22T00:00:00+00:00", "2018-12-23T00:00:00+00:00", "2018-12-24T00:00:00+00:00", "2018-12-25T00:00:00+00:00", "2018-12-26T00:00:00+00:00", "2018-12-27T00:00:00+00:00", "2018-12-28T00:00:00+00:00", "2018-12-29T00:00:00+00:00", "2018-12-30T00:00:00+00:00", "2018-12-31T00:00:00+00:00", "2019-01-01T00:00:00+00:00", "2019-01-02T00:00:00+00:00", "2019-01-03T00:00:00+00:00", "2019-01-04T00:00:00+00:00", "2019-01-05T00:00:00+00:00", "2019-01-06T00:00:00+00:00", "2019-01-07T00:00:00+00:00", "2019-01-08T00:00:00+00:00", "2019-01-09T00:00:00+00:00", "2019-01-10T00:00:00+00:00", "2019-01-11T00:00:00+00:00", "2019-01-12T00:00:00+00:00", "2019-01-13T00:00:00+00:00", "2019-01-14T00:00:00+00:00", "2019-01-15T00:00:00+00:00", "2019-01-16T00:00:00+00:00", "2019-01-17T00:00:00+00:00", "2019-01-18T00:00:00+00:00", "2019-01-19T00:00:00+00:00", "2019-01-20T00:00:00+00:00", "2019-01-21T00:00:00+00:00", "2019-01-22T00:00:00+00:00", "2019-01-23T00:00:00+00:00", "2019-01-24T00:00:00+00:00", "2019-01-25T00:00:00+00:00", "2019-01-26T00:00:00+00:00", "2019-01-27T00:00:00+00:00", "2019-01-28T00:00:00+00:00", "2019-01-29T00:00:00+00:00", "2019-01-30T00:00:00+00:00", "2019-01-31T00:00:00+00:00", "2019-02-01T00:00:00+00:00", "2019-02-02T00:00:00+00:00", "2019-02-03T00:00:00+00:00", "2019-02-04T00:00:00+00:00", "2019-02-05T00:00:00+00:00", "2019-02-06T00:00:00+00:00", "2019-02-07T00:00:00+00:00", "2019-02-08T00:00:00+00:00", "2019-02-09T00:00:00+00:00", "2019-02-10T00:00:00+00:00", "2019-02-11T00:00:00+00:00", "2019-02-12T00:00:00+00:00", "2019-02-13T00:00:00+00:00", "2019-02-14T00:00:00+00:00", "2019-02-15T00:00:00+00:00", "2019-02-16T00:00:00+00:00", "2019-02-17T00:00:00+00:00", "2019-02-18T00:00:00+00:00", "2019-02-19T00:00:00+00:00", "2019-02-20T00:00:00+00:00", "2019-02-21T00:00:00+00:00", "2019-02-22T00:00:00+00:00", "2019-02-23T00:00:00+00:00", "2019-02-24T00:00:00+00:00", "2019-02-25T00:00:00+00:00", "2019-02-26T00:00:00+00:00", "2019-02-27T00:00:00+00:00", "2019-02-28T00:00:00+00:00", "2019-03-01T00:00:00+00:00", "2019-03-02T00:00:00+00:00", "2019-03-03T00:00:00+00:00", "2019-03-04T00:00:00+00:00", "2019-03-05T00:00:00+00:00", "2019-03-06T00:00:00+00:00", "2019-03-07T00:00:00+00:00", "2019-03-08T00:00:00+00:00", "2019-03-09T00:00:00+00:00", "2019-03-10T00:00:00+00:00", "2019-03-11T00:00:00+00:00", "2019-03-12T00:00:00+00:00", "2019-03-13T00:00:00+00:00", "2019-03-14T00:00:00+00:00", "2019-03-15T00:00:00+00:00", "2019-03-16T00:00:00+00:00", "2019-03-17T00:00:00+00:00", "2019-03-18T00:00:00+00:00", "2019-03-19T00:00:00+00:00", "2019-03-20T00:00:00+00:00", "2019-03-21T00:00:00+00:00", "2019-03-22T00:00:00+00:00", "2019-03-23T00:00:00+00:00", "2019-03-24T00:00:00+00:00", "2019-03-25T00:00:00+00:00", "2019-03-26T00:00:00+00:00", "2019-03-27T00:00:00+00:00", "2019-03-28T00:00:00+00:00", "2019-03-29T00:00:00+00:00", "2019-03-30T00:00:00+00:00", "2019-03-31T00:00:00+00:00", "2019-04-01T00:00:00+00:00", "2019-04-02T00:00:00+00:00", "2019-04-03T00:00:00+00:00", "2019-04-04T00:00:00+00:00", "2019-04-05T00:00:00+00:00", "2019-04-06T00:00:00+00:00", "2019-04-07T00:00:00+00:00", "2019-04-08T00:00:00+00:00", "2019-04-09T00:00:00+00:00", "2019-04-10T00:00:00+00:00", "2019-04-11T00:00:00+00:00", "2019-04-12T00:00:00+00:00", "2019-04-13T00:00:00+00:00", "2019-04-14T00:00:00+00:00", "2019-04-15T00:00:00+00:00", "2019-04-16T00:00:00+00:00", "2019-04-17T00:00:00+00:00", "2019-04-18T00:00:00+00:00", "2019-04-19T00:00:00+00:00", "2019-04-20T00:00:00+00:00", "2019-04-21T00:00:00+00:00", "2019-04-22T00:00:00+00:00", "2019-04-23T00:00:00+00:00", "2019-04-24T00:00:00+00:00", "2019-04-25T00:00:00+00:00", "2019-04-26T00:00:00+00:00", "2019-04-27T00:00:00+00:00", "2019-04-28T00:00:00+00:00", "2019-04-29T00:00:00+00:00", "2019-04-30T00:00:00+00:00", "2019-05-01T00:00:00+00:00", "2019-05-02T00:00:00+00:00", "2019-05-03T00:00:00+00:00", "2019-05-04T00:00:00+00:00", "2019-05-05T00:00:00+00:00", "2019-05-06T00:00:00+00:00", "2019-05-07T00:00:00+00:00", "2019-05-08T00:00:00+00:00", "2019-05-09T00:00:00+00:00", "2019-05-10T00:00:00+00:00", "2019-05-11T00:00:00+00:00", "2019-05-12T00:00:00+00:00", "2019-05-13T00:00:00+00:00", "2019-05-14T00:00:00+00:00", "2019-05-15T00:00:00+00:00", "2019-05-16T00:00:00+00:00", "2019-05-17T00:00:00+00:00", "2019-05-18T00:00:00+00:00", "2019-05-19T00:00:00+00:00", "2019-05-20T00:00:00+00:00", "2019-05-21T00:00:00+00:00", "2019-05-22T00:00:00+00:00", "2019-05-23T00:00:00+00:00", "2019-05-24T00:00:00+00:00", "2019-05-25T00:00:00+00:00", "2019-05-26T00:00:00+00:00", "2019-05-27T00:00:00+00:00", "2019-05-28T00:00:00+00:00", "2019-05-29T00:00:00+00:00", "2019-05-30T00:00:00+00:00", "2019-05-31T00:00:00+00:00", "2019-06-01T00:00:00+00:00", "2019-06-02T00:00:00+00:00", "2019-06-03T00:00:00+00:00", "2019-06-04T00:00:00+00:00", "2019-06-05T00:00:00+00:00", "2019-06-06T00:00:00+00:00", "2019-06-07T00:00:00+00:00", "2019-06-08T00:00:00+00:00", "2019-06-09T00:00:00+00:00", "2019-06-10T00:00:00+00:00", "2019-06-11T00:00:00+00:00", "2019-06-12T00:00:00+00:00", "2019-06-13T00:00:00+00:00", "2019-06-14T00:00:00+00:00", "2019-06-15T00:00:00+00:00", "2019-06-16T00:00:00+00:00", "2019-06-17T00:00:00+00:00", "2019-06-18T00:00:00+00:00", "2019-06-19T00:00:00+00:00", "2019-06-20T00:00:00+00:00", "2019-06-21T00:00:00+00:00", "2019-06-22T00:00:00+00:00", "2019-06-23T00:00:00+00:00", "2019-06-24T00:00:00+00:00", "2019-06-25T00:00:00+00:00", "2019-06-26T00:00:00+00:00", "2019-06-27T00:00:00+00:00", "2019-06-28T00:00:00+00:00", "2019-06-29T00:00:00+00:00", "2019-06-30T00:00:00+00:00", "2019-07-01T00:00:00+00:00", "2019-07-02T00:00:00+00:00", "2019-07-03T00:00:00+00:00", "2019-07-04T00:00:00+00:00", "2019-07-05T00:00:00+00:00", "2019-07-06T00:00:00+00:00", "2019-07-07T00:00:00+00:00", "2019-07-08T00:00:00+00:00", "2019-07-09T00:00:00+00:00", "2019-07-10T00:00:00+00:00", "2019-07-11T00:00:00+00:00", "2019-07-12T00:00:00+00:00", "2019-07-13T00:00:00+00:00", "2019-07-14T00:00:00+00:00", "2019-07-15T00:00:00+00:00", "2019-07-16T00:00:00+00:00", "2019-07-17T00:00:00+00:00", "2019-07-18T00:00:00+00:00", "2019-07-19T00:00:00+00:00", "2019-07-20T00:00:00+00:00", "2019-07-21T00:00:00+00:00", "2019-07-22T00:00:00+00:00", "2019-07-23T00:00:00+00:00", "2019-07-24T00:00:00+00:00", "2019-07-25T00:00:00+00:00", "2019-07-26T00:00:00+00:00", "2019-07-27T00:00:00+00:00", "2019-07-28T00:00:00+00:00", "2019-07-29T00:00:00+00:00", "2019-07-30T00:00:00+00:00", "2019-07-31T00:00:00+00:00", "2019-08-01T00:00:00+00:00", "2019-08-02T00:00:00+00:00", "2019-08-03T00:00:00+00:00", "2019-08-04T00:00:00+00:00", "2019-08-05T00:00:00+00:00", "2019-08-06T00:00:00+00:00", "2019-08-07T00:00:00+00:00", "2019-08-08T00:00:00+00:00", "2019-08-09T00:00:00+00:00", "2019-08-10T00:00:00+00:00", "2019-08-11T00:00:00+00:00", "2019-08-12T00:00:00+00:00", "2019-08-13T00:00:00+00:00", "2019-08-14T00:00:00+00:00", "2019-08-15T00:00:00+00:00", "2019-08-16T00:00:00+00:00", "2019-08-17T00:00:00+00:00", "2019-08-18T00:00:00+00:00", "2019-08-19T00:00:00+00:00", "2019-08-20T00:00:00+00:00", "2019-08-21T00:00:00+00:00", "2019-08-22T00:00:00+00:00", "2019-08-23T00:00:00+00:00", "2019-08-24T00:00:00+00:00", "2019-08-25T00:00:00+00:00", "2019-08-26T00:00:00+00:00", "2019-08-27T00:00:00+00:00", "2019-08-28T00:00:00+00:00", "2019-08-29T00:00:00+00:00", "2019-08-30T00:00:00+00:00", "2019-08-31T00:00:00+00:00", "2019-09-01T00:00:00+00:00", "2019-09-02T00:00:00+00:00", "2019-09-03T00:00:00+00:00", "2019-09-04T00:00:00+00:00", "2019-09-05T00:00:00+00:00", "2019-09-06T00:00:00+00:00", "2019-09-07T00:00:00+00:00", "2019-09-08T00:00:00+00:00", "2019-09-09T00:00:00+00:00", "2019-09-10T00:00:00+00:00", "2019-09-11T00:00:00+00:00", "2019-09-12T00:00:00+00:00", "2019-09-13T00:00:00+00:00", "2019-09-14T00:00:00+00:00", "2019-09-15T00:00:00+00:00", "2019-09-16T00:00:00+00:00", "2019-09-17T00:00:00+00:00", "2019-09-18T00:00:00+00:00", "2019-09-19T00:00:00+00:00", "2019-09-20T00:00:00+00:00", "2019-09-21T00:00:00+00:00", "2019-09-22T00:00:00+00:00", "2019-09-23T00:00:00+00:00", "2019-09-24T00:00:00+00:00", "2019-09-25T00:00:00+00:00", "2019-09-26T00:00:00+00:00", "2019-09-27T00:00:00+00:00", "2019-09-28T00:00:00+00:00", "2019-09-29T00:00:00+00:00", "2019-09-30T00:00:00+00:00", "2019-10-01T00:00:00+00:00", "2019-10-02T00:00:00+00:00", "2019-10-03T00:00:00+00:00", "2019-10-04T00:00:00+00:00", "2019-10-05T00:00:00+00:00", "2019-10-06T00:00:00+00:00", "2019-10-07T00:00:00+00:00", "2019-10-08T00:00:00+00:00", "2019-10-09T00:00:00+00:00", "2019-10-10T00:00:00+00:00", "2019-10-11T00:00:00+00:00", "2019-10-12T00:00:00+00:00", "2019-10-13T00:00:00+00:00", "2019-10-14T00:00:00+00:00", "2019-10-15T00:00:00+00:00", "2019-10-16T00:00:00+00:00", "2019-10-17T00:00:00+00:00", "2019-10-18T00:00:00+00:00", "2019-10-19T00:00:00+00:00", "2019-10-20T00:00:00+00:00", "2019-10-21T00:00:00+00:00", "2019-10-22T00:00:00+00:00", "2019-10-23T00:00:00+00:00", "2019-10-24T00:00:00+00:00", "2019-10-25T00:00:00+00:00", "2019-10-26T00:00:00+00:00", "2019-10-27T00:00:00+00:00", "2019-10-28T00:00:00+00:00", "2019-10-29T00:00:00+00:00", "2019-10-30T00:00:00+00:00", "2019-10-31T00:00:00+00:00", "2019-11-01T00:00:00+00:00", "2019-11-02T00:00:00+00:00", "2019-11-03T00:00:00+00:00", "2019-11-04T00:00:00+00:00", "2019-11-05T00:00:00+00:00", "2019-11-06T00:00:00+00:00", "2019-11-07T00:00:00+00:00", "2019-11-08T00:00:00+00:00", "2019-11-09T00:00:00+00:00", "2019-11-10T00:00:00+00:00", "2019-11-11T00:00:00+00:00", "2019-11-12T00:00:00+00:00", "2019-11-13T00:00:00+00:00", "2019-11-14T00:00:00+00:00", "2019-11-15T00:00:00+00:00", "2019-11-16T00:00:00+00:00", "2019-11-17T00:00:00+00:00", "2019-11-18T00:00:00+00:00", "2019-11-19T00:00:00+00:00", "2019-11-20T00:00:00+00:00", "2019-11-21T00:00:00+00:00", "2019-11-22T00:00:00+00:00", "2019-11-23T00:00:00+00:00", "2019-11-24T00:00:00+00:00", "2019-11-25T00:00:00+00:00", "2019-11-26T00:00:00+00:00", "2019-11-27T00:00:00+00:00", "2019-11-28T00:00:00+00:00", "2019-11-29T00:00:00+00:00", "2019-11-30T00:00:00+00:00", "2019-12-01T00:00:00+00:00", "2019-12-02T00:00:00+00:00", "2019-12-03T00:00:00+00:00", "2019-12-04T00:00:00+00:00", "2019-12-05T00:00:00+00:00", "2019-12-06T00:00:00+00:00", "2019-12-07T00:00:00+00:00", "2019-12-08T00:00:00+00:00", "2019-12-09T00:00:00+00:00", "2019-12-10T00:00:00+00:00", "2019-12-11T00:00:00+00:00", "2019-12-12T00:00:00+00:00", "2019-12-13T00:00:00+00:00", "2019-12-14T00:00:00+00:00", "2019-12-15T00:00:00+00:00", "2019-12-16T00:00:00+00:00", "2019-12-17T00:00:00+00:00", "2019-12-18T00:00:00+00:00", "2019-12-19T00:00:00+00:00", "2019-12-20T00:00:00+00:00", "2019-12-21T00:00:00+00:00", "2019-12-22T00:00:00+00:00", "2019-12-23T00:00:00+00:00", "2019-12-24T00:00:00+00:00", "2019-12-25T00:00:00+00:00", "2019-12-26T00:00:00+00:00", "2019-12-27T00:00:00+00:00", "2019-12-28T00:00:00+00:00", "2019-12-29T00:00:00+00:00", "2019-12-30T00:00:00+00:00", "2019-12-31T00:00:00+00:00", "2020-01-01T00:00:00+00:00", "2020-01-02T00:00:00+00:00", "2020-01-03T00:00:00+00:00", "2020-01-04T00:00:00+00:00", "2020-01-05T00:00:00+00:00", "2020-01-06T00:00:00+00:00", "2020-01-07T00:00:00+00:00", "2020-01-08T00:00:00+00:00", "2020-01-09T00:00:00+00:00", "2020-01-10T00:00:00+00:00", "2020-01-11T00:00:00+00:00", "2020-01-12T00:00:00+00:00", "2020-01-13T00:00:00+00:00", "2020-01-14T00:00:00+00:00", "2020-01-15T00:00:00+00:00", "2020-01-16T00:00:00+00:00", "2020-01-17T00:00:00+00:00", "2020-01-18T00:00:00+00:00", "2020-01-19T00:00:00+00:00", "2020-01-20T00:00:00+00:00", "2020-01-21T00:00:00+00:00", "2020-01-22T00:00:00+00:00", "2020-01-23T00:00:00+00:00", "2020-01-24T00:00:00+00:00", "2020-01-25T00:00:00+00:00", "2020-01-26T00:00:00+00:00", "2020-01-27T00:00:00+00:00", "2020-01-28T00:00:00+00:00", "2020-01-29T00:00:00+00:00", "2020-01-30T00:00:00+00:00", "2020-01-31T00:00:00+00:00", "2020-02-01T00:00:00+00:00", "2020-02-02T00:00:00+00:00", "2020-02-03T00:00:00+00:00", "2020-02-04T00:00:00+00:00", "2020-02-05T00:00:00+00:00", "2020-02-06T00:00:00+00:00", "2020-02-07T00:00:00+00:00", "2020-02-08T00:00:00+00:00", "2020-02-09T00:00:00+00:00", "2020-02-10T00:00:00+00:00", "2020-02-11T00:00:00+00:00", "2020-02-12T00:00:00+00:00", "2020-02-13T00:00:00+00:00", "2020-02-14T00:00:00+00:00", "2020-02-15T00:00:00+00:00", "2020-02-16T00:00:00+00:00", "2020-02-17T00:00:00+00:00", "2020-02-18T00:00:00+00:00", "2020-02-19T00:00:00+00:00", "2020-02-20T00:00:00+00:00", "2020-02-21T00:00:00+00:00", "2020-02-22T00:00:00+00:00", "2020-02-23T00:00:00+00:00", "2020-02-24T00:00:00+00:00", "2020-02-25T00:00:00+00:00", "2020-02-26T00:00:00+00:00", "2020-02-27T00:00:00+00:00", "2020-02-28T00:00:00+00:00", "2020-02-29T00:00:00+00:00", "2020-03-01T00:00:00+00:00", "2020-03-02T00:00:00+00:00", "2020-03-03T00:00:00+00:00", "2020-03-04T00:00:00+00:00", "2020-03-05T00:00:00+00:00", "2020-03-06T00:00:00+00:00", "2020-03-07T00:00:00+00:00", "2020-03-08T00:00:00+00:00", "2020-03-09T00:00:00+00:00", "2020-03-10T00:00:00+00:00", "2020-03-11T00:00:00+00:00", "2020-03-12T00:00:00+00:00", "2020-03-13T00:00:00+00:00", "2020-03-14T00:00:00+00:00", "2020-03-15T00:00:00+00:00", "2020-03-16T00:00:00+00:00", "2020-03-17T00:00:00+00:00", "2020-03-18T00:00:00+00:00", "2020-03-19T00:00:00+00:00", "2020-03-20T00:00:00+00:00", "2020-03-21T00:00:00+00:00", "2020-03-22T00:00:00+00:00", "2020-03-23T00:00:00+00:00", "2020-03-24T00:00:00+00:00", "2020-03-25T00:00:00+00:00", "2020-03-26T00:00:00+00:00", "2020-03-27T00:00:00+00:00", "2020-03-28T00:00:00+00:00", "2020-03-29T00:00:00+00:00", "2020-03-30T00:00:00+00:00", "2020-03-31T00:00:00+00:00", "2020-04-01T00:00:00+00:00", "2020-04-02T00:00:00+00:00", "2020-04-03T00:00:00+00:00", "2020-04-04T00:00:00+00:00", "2020-04-05T00:00:00+00:00", "2020-04-06T00:00:00+00:00", "2020-04-07T00:00:00+00:00", "2020-04-08T00:00:00+00:00", "2020-04-09T00:00:00+00:00", "2020-04-10T00:00:00+00:00", "2020-04-11T00:00:00+00:00", "2020-04-12T00:00:00+00:00", "2020-04-13T00:00:00+00:00", "2020-04-14T00:00:00+00:00", "2020-04-15T00:00:00+00:00", "2020-04-16T00:00:00+00:00", "2020-04-17T00:00:00+00:00", "2020-04-18T00:00:00+00:00", "2020-04-19T00:00:00+00:00", "2020-04-20T00:00:00+00:00", "2020-04-21T00:00:00+00:00", "2020-04-22T00:00:00+00:00", "2020-04-23T00:00:00+00:00", "2020-04-24T00:00:00+00:00", "2020-04-25T00:00:00+00:00", "2020-04-26T00:00:00+00:00", "2020-04-27T00:00:00+00:00", "2020-04-28T00:00:00+00:00", "2020-04-29T00:00:00+00:00", "2020-04-30T00:00:00+00:00", "2020-05-01T00:00:00+00:00", "2020-05-02T00:00:00+00:00", "2020-05-03T00:00:00+00:00", "2020-05-04T00:00:00+00:00", "2020-05-05T00:00:00+00:00", "2020-05-06T00:00:00+00:00", "2020-05-07T00:00:00+00:00", "2020-05-08T00:00:00+00:00", "2020-05-09T00:00:00+00:00", "2020-05-10T00:00:00+00:00", "2020-05-11T00:00:00+00:00", "2020-05-12T00:00:00+00:00", "2020-05-13T00:00:00+00:00", "2020-05-14T00:00:00+00:00", "2020-05-15T00:00:00+00:00", "2020-05-16T00:00:00+00:00", "2020-05-17T00:00:00+00:00", "2020-05-18T00:00:00+00:00", "2020-05-19T00:00:00+00:00", "2020-05-20T00:00:00+00:00", "2020-05-21T00:00:00+00:00", "2020-05-22T00:00:00+00:00", "2020-05-23T00:00:00+00:00", "2020-05-24T00:00:00+00:00", "2020-05-25T00:00:00+00:00", "2020-05-26T00:00:00+00:00", "2020-05-27T00:00:00+00:00", "2020-05-28T00:00:00+00:00", "2020-05-29T00:00:00+00:00", "2020-05-30T00:00:00+00:00", "2020-05-31T00:00:00+00:00", "2020-06-01T00:00:00+00:00", "2020-06-02T00:00:00+00:00", "2020-06-03T00:00:00+00:00", "2020-06-04T00:00:00+00:00", "2020-06-05T00:00:00+00:00", "2020-06-06T00:00:00+00:00", "2020-06-07T00:00:00+00:00", "2020-06-08T00:00:00+00:00", "2020-06-09T00:00:00+00:00", "2020-06-10T00:00:00+00:00", "2020-06-11T00:00:00+00:00", "2020-06-12T00:00:00+00:00", "2020-06-13T00:00:00+00:00", "2020-06-14T00:00:00+00:00", "2020-06-15T00:00:00+00:00", "2020-06-16T00:00:00+00:00", "2020-06-17T00:00:00+00:00", "2020-06-18T00:00:00+00:00", "2020-06-19T00:00:00+00:00", "2020-06-20T00:00:00+00:00", "2020-06-21T00:00:00+00:00", "2020-06-22T00:00:00+00:00", "2020-06-23T00:00:00+00:00", "2020-06-24T00:00:00+00:00", "2020-06-25T00:00:00+00:00", "2020-06-26T00:00:00+00:00", "2020-06-27T00:00:00+00:00", "2020-06-28T00:00:00+00:00", "2020-06-29T00:00:00+00:00", "2020-06-30T00:00:00+00:00", "2020-07-01T00:00:00+00:00", "2020-07-02T00:00:00+00:00", "2020-07-03T00:00:00+00:00", "2020-07-04T00:00:00+00:00", "2020-07-05T00:00:00+00:00", "2020-07-06T00:00:00+00:00", "2020-07-07T00:00:00+00:00", "2020-07-08T00:00:00+00:00", "2020-07-09T00:00:00+00:00", "2020-07-10T00:00:00+00:00", "2020-07-11T00:00:00+00:00", "2020-07-12T00:00:00+00:00", "2020-07-13T00:00:00+00:00", "2020-07-14T00:00:00+00:00", "2020-07-15T00:00:00+00:00", "2020-07-16T00:00:00+00:00", "2020-07-17T00:00:00+00:00", "2020-07-18T00:00:00+00:00", "2020-07-19T00:00:00+00:00", "2020-07-20T00:00:00+00:00", "2020-07-21T00:00:00+00:00", "2020-07-22T00:00:00+00:00", "2020-07-23T00:00:00+00:00", "2020-07-24T00:00:00+00:00", "2020-07-25T00:00:00+00:00", "2020-07-26T00:00:00+00:00", "2020-07-27T00:00:00+00:00", "2020-07-28T00:00:00+00:00", "2020-07-29T00:00:00+00:00", "2020-07-30T00:00:00+00:00", "2020-07-31T00:00:00+00:00", "2020-08-01T00:00:00+00:00", "2020-08-02T00:00:00+00:00", "2020-08-03T00:00:00+00:00", "2020-08-04T00:00:00+00:00", "2020-08-05T00:00:00+00:00", "2020-08-06T00:00:00+00:00", "2020-08-07T00:00:00+00:00", "2020-08-08T00:00:00+00:00", "2020-08-09T00:00:00+00:00", "2020-08-10T00:00:00+00:00", "2020-08-11T00:00:00+00:00", "2020-08-12T00:00:00+00:00", "2020-08-13T00:00:00+00:00", "2020-08-14T00:00:00+00:00", "2020-08-15T00:00:00+00:00", "2020-08-16T00:00:00+00:00", "2020-08-17T00:00:00+00:00", "2020-08-18T00:00:00+00:00", "2020-08-19T00:00:00+00:00", "2020-08-20T00:00:00+00:00", "2020-08-21T00:00:00+00:00", "2020-08-22T00:00:00+00:00", "2020-08-23T00:00:00+00:00", "2020-08-24T00:00:00+00:00", "2020-08-25T00:00:00+00:00", "2020-08-26T00:00:00+00:00", "2020-08-27T00:00:00+00:00", "2020-08-28T00:00:00+00:00", "2020-08-29T00:00:00+00:00", "2020-08-30T00:00:00+00:00", "2020-08-31T00:00:00+00:00", "2020-09-01T00:00:00+00:00", "2020-09-02T00:00:00+00:00", "2020-09-03T00:00:00+00:00", "2020-09-04T00:00:00+00:00", "2020-09-05T00:00:00+00:00", "2020-09-06T00:00:00+00:00", "2020-09-07T00:00:00+00:00", "2020-09-08T00:00:00+00:00", "2020-09-09T00:00:00+00:00", "2020-09-10T00:00:00+00:00", "2020-09-11T00:00:00+00:00", "2020-09-12T00:00:00+00:00", "2020-09-13T00:00:00+00:00", "2020-09-14T00:00:00+00:00", "2020-09-15T00:00:00+00:00", "2020-09-16T00:00:00+00:00", "2020-09-17T00:00:00+00:00", "2020-09-18T00:00:00+00:00", "2020-09-19T00:00:00+00:00", "2020-09-20T00:00:00+00:00", "2020-09-21T00:00:00+00:00", "2020-09-22T00:00:00+00:00", "2020-09-23T00:00:00+00:00", "2020-09-24T00:00:00+00:00", "2020-09-25T00:00:00+00:00", "2020-09-26T00:00:00+00:00", "2020-09-27T00:00:00+00:00", "2020-09-28T00:00:00+00:00", "2020-09-29T00:00:00+00:00", "2020-09-30T00:00:00+00:00", "2020-10-01T00:00:00+00:00", "2020-10-02T00:00:00+00:00", "2020-10-03T00:00:00+00:00", "2020-10-04T00:00:00+00:00", "2020-10-05T00:00:00+00:00", "2020-10-06T00:00:00+00:00", "2020-10-07T00:00:00+00:00", "2020-10-08T00:00:00+00:00", "2020-10-09T00:00:00+00:00", "2020-10-10T00:00:00+00:00", "2020-10-11T00:00:00+00:00", "2020-10-12T00:00:00+00:00", "2020-10-13T00:00:00+00:00", "2020-10-14T00:00:00+00:00", "2020-10-15T00:00:00+00:00", "2020-10-16T00:00:00+00:00", "2020-10-17T00:00:00+00:00", "2020-10-18T00:00:00+00:00", "2020-10-19T00:00:00+00:00", "2020-10-20T00:00:00+00:00", "2020-10-21T00:00:00+00:00", "2020-10-22T00:00:00+00:00", "2020-10-23T00:00:00+00:00", "2020-10-24T00:00:00+00:00", "2020-10-25T00:00:00+00:00", "2020-10-26T00:00:00+00:00", "2020-10-27T00:00:00+00:00", "2020-10-28T00:00:00+00:00", "2020-10-29T00:00:00+00:00", "2020-10-30T00:00:00+00:00", "2020-10-31T00:00:00+00:00", "2020-11-01T00:00:00+00:00", "2020-11-02T00:00:00+00:00", "2020-11-03T00:00:00+00:00", "2020-11-04T00:00:00+00:00", "2020-11-05T00:00:00+00:00", "2020-11-06T00:00:00+00:00", "2020-11-07T00:00:00+00:00", "2020-11-08T00:00:00+00:00", "2020-11-09T00:00:00+00:00", "2020-11-10T00:00:00+00:00", "2020-11-11T00:00:00+00:00", "2020-11-12T00:00:00+00:00", "2020-11-13T00:00:00+00:00", "2020-11-14T00:00:00+00:00", "2020-11-15T00:00:00+00:00", "2020-11-16T00:00:00+00:00", "2020-11-17T00:00:00+00:00", "2020-11-18T00:00:00+00:00", "2020-11-19T00:00:00+00:00", "2020-11-20T00:00:00+00:00", "2020-11-21T00:00:00+00:00", "2020-11-22T00:00:00+00:00", "2020-11-23T00:00:00+00:00", "2020-11-24T00:00:00+00:00", "2020-11-25T00:00:00+00:00", "2020-11-26T00:00:00+00:00", "2020-11-27T00:00:00+00:00", "2020-11-28T00:00:00+00:00", "2020-11-29T00:00:00+00:00", "2020-11-30T00:00:00+00:00", "2020-12-01T00:00:00+00:00", "2020-12-02T00:00:00+00:00", "2020-12-03T00:00:00+00:00", "2020-12-04T00:00:00+00:00", "2020-12-05T00:00:00+00:00", "2020-12-06T00:00:00+00:00", "2020-12-07T00:00:00+00:00", "2020-12-08T00:00:00+00:00", "2020-12-09T00:00:00+00:00", "2020-12-10T00:00:00+00:00", "2020-12-11T00:00:00+00:00", "2020-12-12T00:00:00+00:00", "2020-12-13T00:00:00+00:00", "2020-12-14T00:00:00+00:00", "2020-12-15T00:00:00+00:00", "2020-12-16T00:00:00+00:00", "2020-12-17T00:00:00+00:00", "2020-12-18T00:00:00+00:00", "2020-12-19T00:00:00+00:00", "2020-12-20T00:00:00+00:00", "2020-12-21T00:00:00+00:00", "2020-12-22T00:00:00+00:00", "2020-12-23T00:00:00+00:00", "2020-12-24T00:00:00+00:00", "2020-12-25T00:00:00+00:00", "2020-12-26T00:00:00+00:00", "2020-12-27T00:00:00+00:00", "2020-12-28T00:00:00+00:00", "2020-12-29T00:00:00+00:00", "2020-12-30T00:00:00+00:00", "2020-12-31T00:00:00+00:00", "2021-01-01T00:00:00+00:00", "2021-01-02T00:00:00+00:00", "2021-01-03T00:00:00+00:00", "2021-01-04T00:00:00+00:00", "2021-01-05T00:00:00+00:00", "2021-01-06T00:00:00+00:00", "2021-01-07T00:00:00+00:00", "2021-01-08T00:00:00+00:00", "2021-01-09T00:00:00+00:00", "2021-01-10T00:00:00+00:00", "2021-01-11T00:00:00+00:00", "2021-01-12T00:00:00+00:00", "2021-01-13T00:00:00+00:00", "2021-01-14T00:00:00+00:00", "2021-01-15T00:00:00+00:00", "2021-01-16T00:00:00+00:00", "2021-01-17T00:00:00+00:00", "2021-01-18T00:00:00+00:00", "2021-01-19T00:00:00+00:00", "2021-01-20T00:00:00+00:00", "2021-01-21T00:00:00+00:00", "2021-01-22T00:00:00+00:00", "2021-01-23T00:00:00+00:00", "2021-01-24T00:00:00+00:00", "2021-01-25T00:00:00+00:00", "2021-01-26T00:00:00+00:00", "2021-01-27T00:00:00+00:00", "2021-01-28T00:00:00+00:00", "2021-01-29T00:00:00+00:00", "2021-01-30T00:00:00+00:00", "2021-01-31T00:00:00+00:00", "2021-02-01T00:00:00+00:00", "2021-02-02T00:00:00+00:00", "2021-02-03T00:00:00+00:00", "2021-02-04T00:00:00+00:00", "2021-02-05T00:00:00+00:00", "2021-02-06T00:00:00+00:00", "2021-02-07T00:00:00+00:00", "2021-02-08T00:00:00+00:00", "2021-02-09T00:00:00+00:00", "2021-02-10T00:00:00+00:00", "2021-02-11T00:00:00+00:00", "2021-02-12T00:00:00+00:00", "2021-02-13T00:00:00+00:00", "2021-02-14T00:00:00+00:00", "2021-02-15T00:00:00+00:00", "2021-02-16T00:00:00+00:00", "2021-02-17T00:00:00+00:00", "2021-02-18T00:00:00+00:00", "2021-02-19T00:00:00+00:00", "2021-02-20T00:00:00+00:00", "2021-02-21T00:00:00+00:00", "2021-02-22T00:00:00+00:00", "2021-02-23T00:00:00+00:00", "2021-02-24T00:00:00+00:00", "2021-02-25T00:00:00+00:00", "2021-02-26T00:00:00+00:00", "2021-02-27T00:00:00+00:00", "2021-02-28T00:00:00+00:00", "2021-03-01T00:00:00+00:00", "2021-03-02T00:00:00+00:00", "2021-03-03T00:00:00+00:00", "2021-03-04T00:00:00+00:00", "2021-03-05T00:00:00+00:00", "2021-03-06T00:00:00+00:00", "2021-03-07T00:00:00+00:00", "2021-03-08T00:00:00+00:00", "2021-03-09T00:00:00+00:00", "2021-03-10T00:00:00+00:00", "2021-03-11T00:00:00+00:00", "2021-03-12T00:00:00+00:00", "2021-03-13T00:00:00+00:00", "2021-03-14T00:00:00+00:00", "2021-03-15T00:00:00+00:00", "2021-03-16T00:00:00+00:00", "2021-03-17T00:00:00+00:00", "2021-03-18T00:00:00+00:00", "2021-03-19T00:00:00+00:00", "2021-03-20T00:00:00+00:00", "2021-03-21T00:00:00+00:00", "2021-03-22T00:00:00+00:00", "2021-03-23T00:00:00+00:00", "2021-03-24T00:00:00+00:00", "2021-03-25T00:00:00+00:00", "2021-03-26T00:00:00+00:00", "2021-03-27T00:00:00+00:00", "2021-03-28T00:00:00+00:00", "2021-03-29T00:00:00+00:00", "2021-03-30T00:00:00+00:00", "2021-03-31T00:00:00+00:00", "2021-04-01T00:00:00+00:00", "2021-04-02T00:00:00+00:00", "2021-04-03T00:00:00+00:00", "2021-04-04T00:00:00+00:00", "2021-04-05T00:00:00+00:00", "2021-04-06T00:00:00+00:00", "2021-04-07T00:00:00+00:00", "2021-04-08T00:00:00+00:00", "2021-04-09T00:00:00+00:00", "2021-04-10T00:00:00+00:00", "2021-04-11T00:00:00+00:00", "2021-04-12T00:00:00+00:00", "2021-04-13T00:00:00+00:00", "2021-04-14T00:00:00+00:00", "2021-04-15T00:00:00+00:00", "2021-04-16T00:00:00+00:00", "2021-04-17T00:00:00+00:00", "2021-04-18T00:00:00+00:00", "2021-04-19T00:00:00+00:00", "2021-04-20T00:00:00+00:00", "2021-04-21T00:00:00+00:00", "2021-04-22T00:00:00+00:00", "2021-04-23T00:00:00+00:00", "2021-04-24T00:00:00+00:00", "2021-04-25T00:00:00+00:00", "2021-04-26T00:00:00+00:00", "2021-04-27T00:00:00+00:00", "2021-04-28T00:00:00+00:00", "2021-04-29T00:00:00+00:00", "2021-04-30T00:00:00+00:00", "2021-05-01T00:00:00+00:00", "2021-05-02T00:00:00+00:00", "2021-05-03T00:00:00+00:00", "2021-05-04T00:00:00+00:00", "2021-05-05T00:00:00+00:00", "2021-05-06T00:00:00+00:00", "2021-05-07T00:00:00+00:00", "2021-05-08T00:00:00+00:00", "2021-05-09T00:00:00+00:00", "2021-05-10T00:00:00+00:00", "2021-05-11T00:00:00+00:00", "2021-05-12T00:00:00+00:00", "2021-05-13T00:00:00+00:00", "2021-05-14T00:00:00+00:00", "2021-05-15T00:00:00+00:00", "2021-05-16T00:00:00+00:00", "2021-05-17T00:00:00+00:00", "2021-05-18T00:00:00+00:00", "2021-05-19T00:00:00+00:00", "2021-05-20T00:00:00+00:00", "2021-05-21T00:00:00+00:00", "2021-05-22T00:00:00+00:00", "2021-05-23T00:00:00+00:00", "2021-05-24T00:00:00+00:00", "2021-05-25T00:00:00+00:00", "2021-05-26T00:00:00+00:00", "2021-05-27T00:00:00+00:00", "2021-05-28T00:00:00+00:00", "2021-05-29T00:00:00+00:00", "2021-05-30T00:00:00+00:00", "2021-05-31T00:00:00+00:00", "2021-06-01T00:00:00+00:00", "2021-06-02T00:00:00+00:00", "2021-06-03T00:00:00+00:00", "2021-06-04T00:00:00+00:00", "2021-06-05T00:00:00+00:00", "2021-06-06T00:00:00+00:00", "2021-06-07T00:00:00+00:00", "2021-06-08T00:00:00+00:00", "2021-06-09T00:00:00+00:00", "2021-06-10T00:00:00+00:00", "2021-06-11T00:00:00+00:00", "2021-06-12T00:00:00+00:00", "2021-06-13T00:00:00+00:00", "2021-06-14T00:00:00+00:00", "2021-06-15T00:00:00+00:00", "2021-06-16T00:00:00+00:00", "2021-06-17T00:00:00+00:00", "2021-06-18T00:00:00+00:00", "2021-06-19T00:00:00+00:00", "2021-06-20T00:00:00+00:00", "2021-06-21T00:00:00+00:00", "2021-06-22T00:00:00+00:00", "2021-06-23T00:00:00+00:00", "2021-06-24T00:00:00+00:00", "2021-06-25T00:00:00+00:00", "2021-06-26T00:00:00+00:00", "2021-06-27T00:00:00+00:00", "2021-06-28T00:00:00+00:00", "2021-06-29T00:00:00+00:00", "2021-06-30T00:00:00+00:00", "2021-07-01T00:00:00+00:00", "2021-07-02T00:00:00+00:00", "2021-07-03T00:00:00+00:00", "2021-07-04T00:00:00+00:00", "2021-07-05T00:00:00+00:00", "2021-07-06T00:00:00+00:00", "2021-07-07T00:00:00+00:00", "2021-07-08T00:00:00+00:00", "2021-07-09T00:00:00+00:00", "2021-07-10T00:00:00+00:00", "2021-07-11T00:00:00+00:00", "2021-07-12T00:00:00+00:00", "2021-07-13T00:00:00+00:00", "2021-07-14T00:00:00+00:00", "2021-07-15T00:00:00+00:00", "2021-07-16T00:00:00+00:00", "2021-07-17T00:00:00+00:00", "2021-07-18T00:00:00+00:00", "2021-07-19T00:00:00+00:00", "2021-07-20T00:00:00+00:00", "2021-07-21T00:00:00+00:00", "2021-07-22T00:00:00+00:00", "2021-07-23T00:00:00+00:00", "2021-07-24T00:00:00+00:00", "2021-07-25T00:00:00+00:00", "2021-07-26T00:00:00+00:00", "2021-07-27T00:00:00+00:00", "2021-07-28T00:00:00+00:00", "2021-07-29T00:00:00+00:00", "2021-07-30T00:00:00+00:00", "2021-07-31T00:00:00+00:00", "2021-08-01T00:00:00+00:00", "2021-08-02T00:00:00+00:00", "2021-08-03T00:00:00+00:00", "2021-08-04T00:00:00+00:00", "2021-08-05T00:00:00+00:00", "2021-08-06T00:00:00+00:00", "2021-08-07T00:00:00+00:00", "2021-08-08T00:00:00+00:00", "2021-08-09T00:00:00+00:00", "2021-08-10T00:00:00+00:00", "2021-08-11T00:00:00+00:00", "2021-08-12T00:00:00+00:00", "2021-08-13T00:00:00+00:00", "2021-08-14T00:00:00+00:00", "2021-08-15T00:00:00+00:00", "2021-08-16T00:00:00+00:00", "2021-08-17T00:00:00+00:00", "2021-08-18T00:00:00+00:00", "2021-08-19T00:00:00+00:00", "2021-08-20T00:00:00+00:00", "2021-08-21T00:00:00+00:00", "2021-08-22T00:00:00+00:00", "2021-08-23T00:00:00+00:00", "2021-08-24T00:00:00+00:00", "2021-08-25T00:00:00+00:00", "2021-08-26T00:00:00+00:00", "2021-08-27T00:00:00+00:00", "2021-08-28T00:00:00+00:00", "2021-08-29T00:00:00+00:00", "2021-08-30T00:00:00+00:00", "2021-08-31T00:00:00+00:00", "2021-09-01T00:00:00+00:00", "2021-09-02T00:00:00+00:00", "2021-09-03T00:00:00+00:00", "2021-09-04T00:00:00+00:00", "2021-09-05T00:00:00+00:00", "2021-09-06T00:00:00+00:00", "2021-09-07T00:00:00+00:00", "2021-09-08T00:00:00+00:00", "2021-09-09T00:00:00+00:00", "2021-09-10T00:00:00+00:00", "2021-09-11T00:00:00+00:00", "2021-09-12T00:00:00+00:00", "2021-09-13T00:00:00+00:00", "2021-09-14T00:00:00+00:00", "2021-09-15T00:00:00+00:00", "2021-09-16T00:00:00+00:00", "2021-09-17T00:00:00+00:00", "2021-09-18T00:00:00+00:00", "2021-09-19T00:00:00+00:00", "2021-09-20T00:00:00+00:00", "2021-09-21T00:00:00+00:00", "2021-09-22T00:00:00+00:00", "2021-09-23T00:00:00+00:00", "2021-09-24T00:00:00+00:00", "2021-09-25T00:00:00+00:00", "2021-09-26T00:00:00+00:00", "2021-09-27T00:00:00+00:00", "2021-09-28T00:00:00+00:00", "2021-09-29T00:00:00+00:00", "2021-09-30T00:00:00+00:00", "2021-10-01T00:00:00+00:00", "2021-10-02T00:00:00+00:00", "2021-10-03T00:00:00+00:00", "2021-10-04T00:00:00+00:00", "2021-10-05T00:00:00+00:00", "2021-10-06T00:00:00+00:00", "2021-10-07T00:00:00+00:00", "2021-10-08T00:00:00+00:00", "2021-10-09T00:00:00+00:00", "2021-10-10T00:00:00+00:00", "2021-10-11T00:00:00+00:00", "2021-10-12T00:00:00+00:00", "2021-10-13T00:00:00+00:00", "2021-10-14T00:00:00+00:00", "2021-10-15T00:00:00+00:00", "2021-10-16T00:00:00+00:00", "2021-10-17T00:00:00+00:00", "2021-10-18T00:00:00+00:00", "2021-10-19T00:00:00+00:00", "2021-10-20T00:00:00+00:00", "2021-10-21T00:00:00+00:00", "2021-10-22T00:00:00+00:00", "2021-10-23T00:00:00+00:00", "2021-10-24T00:00:00+00:00", "2021-10-25T00:00:00+00:00", "2021-10-26T00:00:00+00:00", "2021-10-27T00:00:00+00:00", "2021-10-28T00:00:00+00:00", "2021-10-29T00:00:00+00:00", "2021-10-30T00:00:00+00:00", "2021-10-31T00:00:00+00:00", "2021-11-01T00:00:00+00:00", "2021-11-02T00:00:00+00:00", "2021-11-03T00:00:00+00:00", "2021-11-04T00:00:00+00:00", "2021-11-05T00:00:00+00:00", "2021-11-06T00:00:00+00:00", "2021-11-07T00:00:00+00:00", "2021-11-08T00:00:00+00:00", "2021-11-09T00:00:00+00:00", "2021-11-10T00:00:00+00:00", "2021-11-11T00:00:00+00:00", "2021-11-12T00:00:00+00:00", "2021-11-13T00:00:00+00:00", "2021-11-14T00:00:00+00:00", "2021-11-15T00:00:00+00:00", "2021-11-16T00:00:00+00:00", "2021-11-17T00:00:00+00:00", "2021-11-18T00:00:00+00:00", "2021-11-19T00:00:00+00:00", "2021-11-20T00:00:00+00:00", "2021-11-21T00:00:00+00:00", "2021-11-22T00:00:00+00:00", "2021-11-23T00:00:00+00:00", "2021-11-24T00:00:00+00:00", "2021-11-25T00:00:00+00:00", "2021-11-26T00:00:00+00:00", "2021-11-27T00:00:00+00:00", "2021-11-28T00:00:00+00:00", "2021-11-29T00:00:00+00:00", "2021-11-30T00:00:00+00:00", "2021-12-01T00:00:00+00:00", "2021-12-02T00:00:00+00:00", "2021-12-03T00:00:00+00:00", "2021-12-04T00:00:00+00:00", "2021-12-05T00:00:00+00:00", "2021-12-06T00:00:00+00:00", "2021-12-07T00:00:00+00:00", "2021-12-08T00:00:00+00:00", "2021-12-09T00:00:00+00:00", "2021-12-10T00:00:00+00:00", "2021-12-11T00:00:00+00:00", "2021-12-12T00:00:00+00:00", "2021-12-13T00:00:00+00:00", "2021-12-14T00:00:00+00:00", "2021-12-15T00:00:00+00:00", "2021-12-16T00:00:00+00:00", "2021-12-17T00:00:00+00:00", "2021-12-18T00:00:00+00:00", "2021-12-19T00:00:00+00:00", "2021-12-20T00:00:00+00:00", "2021-12-21T00:00:00+00:00", "2021-12-22T00:00:00+00:00", "2021-12-23T00:00:00+00:00", "2021-12-24T00:00:00+00:00", "2021-12-25T00:00:00+00:00", "2021-12-26T00:00:00+00:00", "2021-12-27T00:00:00+00:00", "2021-12-28T00:00:00+00:00", "2021-12-29T00:00:00+00:00", "2021-12-30T00:00:00+00:00", "2021-12-31T00:00:00+00:00", "2022-01-01T00:00:00+00:00", "2022-01-02T00:00:00+00:00", "2022-01-03T00:00:00+00:00", "2022-01-04T00:00:00+00:00", "2022-01-05T00:00:00+00:00", "2022-01-06T00:00:00+00:00", "2022-01-07T00:00:00+00:00", "2022-01-08T00:00:00+00:00", "2022-01-09T00:00:00+00:00", "2022-01-10T00:00:00+00:00", "2022-01-11T00:00:00+00:00", "2022-01-12T00:00:00+00:00", "2022-01-13T00:00:00+00:00", "2022-01-14T00:00:00+00:00", "2022-01-15T00:00:00+00:00", "2022-01-16T00:00:00+00:00", "2022-01-17T00:00:00+00:00", "2022-01-18T00:00:00+00:00", "2022-01-19T00:00:00+00:00", "2022-01-20T00:00:00+00:00", "2022-01-21T00:00:00+00:00", "2022-01-22T00:00:00+00:00", "2022-01-23T00:00:00+00:00", "2022-01-24T00:00:00+00:00", "2022-01-25T00:00:00+00:00", "2022-01-26T00:00:00+00:00", "2022-01-27T00:00:00+00:00", "2022-01-28T00:00:00+00:00", "2022-01-29T00:00:00+00:00", "2022-01-30T00:00:00+00:00", "2022-01-31T00:00:00+00:00", "2022-02-01T00:00:00+00:00", "2022-02-02T00:00:00+00:00", "2022-02-03T00:00:00+00:00", "2022-02-04T00:00:00+00:00", "2022-02-05T00:00:00+00:00", "2022-02-06T00:00:00+00:00", "2022-02-07T00:00:00+00:00", "2022-02-08T00:00:00+00:00", "2022-02-09T00:00:00+00:00", "2022-02-10T00:00:00+00:00", "2022-02-11T00:00:00+00:00", "2022-02-12T00:00:00+00:00", "2022-02-13T00:00:00+00:00", "2022-02-14T00:00:00+00:00", "2022-02-15T00:00:00+00:00", "2022-02-16T00:00:00+00:00", "2022-02-17T00:00:00+00:00", "2022-02-18T00:00:00+00:00", "2022-02-19T00:00:00+00:00", "2022-02-20T00:00:00+00:00", "2022-02-21T00:00:00+00:00", "2022-02-22T00:00:00+00:00", "2022-02-23T00:00:00+00:00", "2022-02-24T00:00:00+00:00", "2022-02-25T00:00:00+00:00", "2022-02-26T00:00:00+00:00", "2022-02-27T00:00:00+00:00", "2022-02-28T00:00:00+00:00", "2022-03-01T00:00:00+00:00", "2022-03-02T00:00:00+00:00", "2022-03-03T00:00:00+00:00", "2022-03-04T00:00:00+00:00", "2022-03-05T00:00:00+00:00", "2022-03-06T00:00:00+00:00", "2022-03-07T00:00:00+00:00", "2022-03-08T00:00:00+00:00", "2022-03-09T00:00:00+00:00", "2022-03-10T00:00:00+00:00", "2022-03-11T00:00:00+00:00", "2022-03-12T00:00:00+00:00", "2022-03-13T00:00:00+00:00", "2022-03-14T00:00:00+00:00", "2022-03-15T00:00:00+00:00", "2022-03-16T00:00:00+00:00", "2022-03-17T00:00:00+00:00", "2022-03-18T00:00:00+00:00", "2022-03-19T00:00:00+00:00", "2022-03-20T00:00:00+00:00", "2022-03-21T00:00:00+00:00", "2022-03-22T00:00:00+00:00", "2022-03-23T00:00:00+00:00", "2022-03-24T00:00:00+00:00", "2022-03-25T00:00:00+00:00", "2022-03-26T00:00:00+00:00", "2022-03-27T00:00:00+00:00", "2022-03-28T00:00:00+00:00", "2022-03-29T00:00:00+00:00", "2022-03-30T00:00:00+00:00", "2022-03-31T00:00:00+00:00", "2022-04-01T00:00:00+00:00", "2022-04-02T00:00:00+00:00", "2022-04-03T00:00:00+00:00", "2022-04-04T00:00:00+00:00", "2022-04-05T00:00:00+00:00", "2022-04-06T00:00:00+00:00", "2022-04-07T00:00:00+00:00", "2022-04-08T00:00:00+00:00", "2022-04-09T00:00:00+00:00", "2022-04-10T00:00:00+00:00", "2022-04-11T00:00:00+00:00", "2022-04-12T00:00:00+00:00", "2022-04-13T00:00:00+00:00", "2022-04-14T00:00:00+00:00", "2022-04-15T00:00:00+00:00", "2022-04-16T00:00:00+00:00", "2022-04-17T00:00:00+00:00", "2022-04-18T00:00:00+00:00", "2022-04-19T00:00:00+00:00", "2022-04-20T00:00:00+00:00", "2022-04-21T00:00:00+00:00", "2022-04-22T00:00:00+00:00", "2022-04-23T00:00:00+00:00", "2022-04-24T00:00:00+00:00", "2022-04-25T00:00:00+00:00", "2022-04-26T00:00:00+00:00", "2022-04-27T00:00:00+00:00", "2022-04-28T00:00:00+00:00", "2022-04-29T00:00:00+00:00", "2022-04-30T00:00:00+00:00", "2022-05-01T00:00:00+00:00", "2022-05-02T00:00:00+00:00", "2022-05-03T00:00:00+00:00", "2022-05-04T00:00:00+00:00", "2022-05-05T00:00:00+00:00", "2022-05-06T00:00:00+00:00", "2022-05-07T00:00:00+00:00", "2022-05-08T00:00:00+00:00", "2022-05-09T00:00:00+00:00", "2022-05-10T00:00:00+00:00", "2022-05-11T00:00:00+00:00", "2022-05-12T00:00:00+00:00", "2022-05-13T00:00:00+00:00", "2022-05-14T00:00:00+00:00", "2022-05-15T00:00:00+00:00", "2022-05-16T00:00:00+00:00", "2022-05-17T00:00:00+00:00", "2022-05-18T00:00:00+00:00", "2022-05-19T00:00:00+00:00", "2022-05-20T00:00:00+00:00", "2022-05-21T00:00:00+00:00", "2022-05-22T00:00:00+00:00", "2022-05-23T00:00:00+00:00", "2022-05-24T00:00:00+00:00", "2022-05-25T00:00:00+00:00", "2022-05-26T00:00:00+00:00", "2022-05-27T00:00:00+00:00", "2022-05-28T00:00:00+00:00", "2022-05-29T00:00:00+00:00", "2022-05-30T00:00:00+00:00", "2022-05-31T00:00:00+00:00", "2022-06-01T00:00:00+00:00", "2022-06-02T00:00:00+00:00", "2022-06-03T00:00:00+00:00", "2022-06-04T00:00:00+00:00", "2022-06-05T00:00:00+00:00", "2022-06-06T00:00:00+00:00", "2022-06-07T00:00:00+00:00", "2022-06-08T00:00:00+00:00", "2022-06-09T00:00:00+00:00", "2022-06-10T00:00:00+00:00", "2022-06-11T00:00:00+00:00", "2022-06-12T00:00:00+00:00", "2022-06-13T00:00:00+00:00", "2022-06-14T00:00:00+00:00", "2022-06-15T00:00:00+00:00", "2022-06-16T00:00:00+00:00", "2022-06-17T00:00:00+00:00", "2022-06-18T00:00:00+00:00", "2022-06-19T00:00:00+00:00", "2022-06-20T00:00:00+00:00", "2022-06-21T00:00:00+00:00", "2022-06-22T00:00:00+00:00", "2022-06-23T00:00:00+00:00", "2022-06-24T00:00:00+00:00", "2022-06-25T00:00:00+00:00", "2022-06-26T00:00:00+00:00", "2022-06-27T00:00:00+00:00", "2022-06-28T00:00:00+00:00", "2022-06-29T00:00:00+00:00", "2022-06-30T00:00:00+00:00", "2022-07-01T00:00:00+00:00", "2022-07-02T00:00:00+00:00", "2022-07-03T00:00:00+00:00", "2022-07-04T00:00:00+00:00", "2022-07-05T00:00:00+00:00", "2022-07-06T00:00:00+00:00", "2022-07-07T00:00:00+00:00", "2022-07-08T00:00:00+00:00", "2022-07-09T00:00:00+00:00", "2022-07-10T00:00:00+00:00", "2022-07-11T00:00:00+00:00", "2022-07-12T00:00:00+00:00", "2022-07-13T00:00:00+00:00", "2022-07-14T00:00:00+00:00", "2022-07-15T00:00:00+00:00", "2022-07-16T00:00:00+00:00", "2022-07-17T00:00:00+00:00", "2022-07-18T00:00:00+00:00", "2022-07-19T00:00:00+00:00", "2022-07-20T00:00:00+00:00", "2022-07-21T00:00:00+00:00", "2022-07-22T00:00:00+00:00", "2022-07-23T00:00:00+00:00", "2022-07-24T00:00:00+00:00", "2022-07-25T00:00:00+00:00", "2022-07-26T00:00:00+00:00", "2022-07-27T00:00:00+00:00", "2022-07-28T00:00:00+00:00", "2022-07-29T00:00:00+00:00", "2022-07-30T00:00:00+00:00", "2022-07-31T00:00:00+00:00", "2022-08-01T00:00:00+00:00", "2022-08-02T00:00:00+00:00", "2022-08-03T00:00:00+00:00", "2022-08-04T00:00:00+00:00", "2022-08-05T00:00:00+00:00", "2022-08-06T00:00:00+00:00", "2022-08-07T00:00:00+00:00", "2022-08-08T00:00:00+00:00", "2022-08-09T00:00:00+00:00", "2022-08-10T00:00:00+00:00", "2022-08-11T00:00:00+00:00", "2022-08-12T00:00:00+00:00", "2022-08-13T00:00:00+00:00", "2022-08-14T00:00:00+00:00", "2022-08-15T00:00:00+00:00", "2022-08-16T00:00:00+00:00", "2022-08-17T00:00:00+00:00", "2022-08-18T00:00:00+00:00", "2022-08-19T00:00:00+00:00", "2022-08-20T00:00:00+00:00", "2022-08-21T00:00:00+00:00", "2022-08-22T00:00:00+00:00", "2022-08-23T00:00:00+00:00", "2022-08-24T00:00:00+00:00", "2022-08-25T00:00:00+00:00", "2022-08-26T00:00:00+00:00", "2022-08-27T00:00:00+00:00", "2022-08-28T00:00:00+00:00", "2022-08-29T00:00:00+00:00", "2022-08-30T00:00:00+00:00", "2022-08-31T00:00:00+00:00", "2022-09-01T00:00:00+00:00", "2022-09-02T00:00:00+00:00", "2022-09-03T00:00:00+00:00", "2022-09-04T00:00:00+00:00", "2022-09-05T00:00:00+00:00", "2022-09-06T00:00:00+00:00", "2022-09-07T00:00:00+00:00", "2022-09-08T00:00:00+00:00", "2022-09-09T00:00:00+00:00", "2022-09-10T00:00:00+00:00", "2022-09-11T00:00:00+00:00", "2022-09-12T00:00:00+00:00", "2022-09-13T00:00:00+00:00", "2022-09-14T00:00:00+00:00", "2022-09-15T00:00:00+00:00", "2022-09-16T00:00:00+00:00", "2022-09-17T00:00:00+00:00", "2022-09-18T00:00:00+00:00", "2022-09-19T00:00:00+00:00", "2022-09-20T00:00:00+00:00", "2022-09-21T00:00:00+00:00", "2022-09-22T00:00:00+00:00", "2022-09-23T00:00:00+00:00", "2022-09-24T00:00:00+00:00", "2022-09-25T00:00:00+00:00", "2022-09-26T00:00:00+00:00", "2022-09-27T00:00:00+00:00", "2022-09-28T00:00:00+00:00", "2022-09-29T00:00:00+00:00", "2022-09-30T00:00:00+00:00", "2022-10-01T00:00:00+00:00", "2022-10-02T00:00:00+00:00", "2022-10-03T00:00:00+00:00", "2022-10-04T00:00:00+00:00", "2022-10-05T00:00:00+00:00", "2022-10-06T00:00:00+00:00", "2022-10-07T00:00:00+00:00", "2022-10-08T00:00:00+00:00", "2022-10-09T00:00:00+00:00", "2022-10-10T00:00:00+00:00", "2022-10-11T00:00:00+00:00", "2022-10-12T00:00:00+00:00", "2022-10-13T00:00:00+00:00", "2022-10-14T00:00:00+00:00", "2022-10-15T00:00:00+00:00", "2022-10-16T00:00:00+00:00", "2022-10-17T00:00:00+00:00", "2022-10-18T00:00:00+00:00", "2022-10-19T00:00:00+00:00", "2022-10-20T00:00:00+00:00", "2022-10-21T00:00:00+00:00", "2022-10-22T00:00:00+00:00", "2022-10-23T00:00:00+00:00", "2022-10-24T00:00:00+00:00", "2022-10-25T00:00:00+00:00", "2022-10-26T00:00:00+00:00", "2022-10-27T00:00:00+00:00", "2022-10-28T00:00:00+00:00", "2022-10-29T00:00:00+00:00", "2022-10-30T00:00:00+00:00", "2022-10-31T00:00:00+00:00", "2022-11-01T00:00:00+00:00", "2022-11-02T00:00:00+00:00", "2022-11-03T00:00:00+00:00", "2022-11-04T00:00:00+00:00", "2022-11-05T00:00:00+00:00", "2022-11-06T00:00:00+00:00", "2022-11-07T00:00:00+00:00", "2022-11-08T00:00:00+00:00", "2022-11-09T00:00:00+00:00", "2022-11-10T00:00:00+00:00", "2022-11-11T00:00:00+00:00", "2022-11-12T00:00:00+00:00", "2022-11-13T00:00:00+00:00", "2022-11-14T00:00:00+00:00", "2022-11-15T00:00:00+00:00", "2022-11-16T00:00:00+00:00", "2022-11-17T00:00:00+00:00", "2022-11-18T00:00:00+00:00", "2022-11-19T00:00:00+00:00", "2022-11-20T00:00:00+00:00", "2022-11-21T00:00:00+00:00", "2022-11-22T00:00:00+00:00", "2022-11-23T00:00:00+00:00", "2022-11-24T00:00:00+00:00", "2022-11-25T00:00:00+00:00", "2022-11-26T00:00:00+00:00", "2022-11-27T00:00:00+00:00", "2022-11-28T00:00:00+00:00", "2022-11-29T00:00:00+00:00", "2022-11-30T00:00:00+00:00", "2022-12-01T00:00:00+00:00", "2022-12-02T00:00:00+00:00", "2022-12-03T00:00:00+00:00", "2022-12-04T00:00:00+00:00", "2022-12-05T00:00:00+00:00", "2022-12-06T00:00:00+00:00", "2022-12-07T00:00:00+00:00", "2022-12-08T00:00:00+00:00", "2022-12-09T00:00:00+00:00", "2022-12-10T00:00:00+00:00", "2022-12-11T00:00:00+00:00", "2022-12-12T00:00:00+00:00", "2022-12-13T00:00:00+00:00", "2022-12-14T00:00:00+00:00", "2022-12-15T00:00:00+00:00", "2022-12-16T00:00:00+00:00", "2022-12-17T00:00:00+00:00", "2022-12-18T00:00:00+00:00", "2022-12-19T00:00:00+00:00", "2022-12-20T00:00:00+00:00", "2022-12-21T00:00:00+00:00", "2022-12-22T00:00:00+00:00", "2022-12-23T00:00:00+00:00", "2022-12-24T00:00:00+00:00", "2022-12-25T00:00:00+00:00", "2022-12-26T00:00:00+00:00", "2022-12-27T00:00:00+00:00", "2022-12-28T00:00:00+00:00", "2022-12-29T00:00:00+00:00", "2022-12-30T00:00:00+00:00", "2022-12-31T00:00:00+00:00", "2023-01-01T00:00:00+00:00", "2023-01-02T00:00:00+00:00", "2023-01-03T00:00:00+00:00", "2023-01-04T00:00:00+00:00", "2023-01-05T00:00:00+00:00", "2023-01-06T00:00:00+00:00", "2023-01-07T00:00:00+00:00", "2023-01-08T00:00:00+00:00", "2023-01-09T00:00:00+00:00", "2023-01-10T00:00:00+00:00", "2023-01-11T00:00:00+00:00", "2023-01-12T00:00:00+00:00", "2023-01-13T00:00:00+00:00", "2023-01-14T00:00:00+00:00", "2023-01-15T00:00:00+00:00", "2023-01-16T00:00:00+00:00", "2023-01-17T00:00:00+00:00", "2023-01-18T00:00:00+00:00", "2023-01-19T00:00:00+00:00", "2023-01-20T00:00:00+00:00", "2023-01-21T00:00:00+00:00", "2023-01-22T00:00:00+00:00", "2023-01-23T00:00:00+00:00", "2023-01-24T00:00:00+00:00", "2023-01-25T00:00:00+00:00", "2023-01-26T00:00:00+00:00", "2023-01-27T00:00:00+00:00", "2023-01-28T00:00:00+00:00", "2023-01-29T00:00:00+00:00", "2023-01-30T00:00:00+00:00", "2023-01-31T00:00:00+00:00", "2023-02-01T00:00:00+00:00", "2023-02-02T00:00:00+00:00", "2023-02-03T00:00:00+00:00", "2023-02-04T00:00:00+00:00", "2023-02-05T00:00:00+00:00", "2023-02-06T00:00:00+00:00", "2023-02-07T00:00:00+00:00", "2023-02-08T00:00:00+00:00", "2023-02-09T00:00:00+00:00", "2023-02-10T00:00:00+00:00", "2023-02-11T00:00:00+00:00", "2023-02-12T00:00:00+00:00", "2023-02-13T00:00:00+00:00", "2023-02-14T00:00:00+00:00", "2023-02-15T00:00:00+00:00", "2023-02-16T00:00:00+00:00", "2023-02-17T00:00:00+00:00", "2023-02-18T00:00:00+00:00", "2023-02-19T00:00:00+00:00", "2023-02-20T00:00:00+00:00", "2023-02-21T00:00:00+00:00", "2023-02-22T00:00:00+00:00", "2023-02-23T00:00:00+00:00", "2023-02-24T00:00:00+00:00", "2023-02-25T00:00:00+00:00", "2023-02-26T00:00:00+00:00", "2023-02-27T00:00:00+00:00", "2023-02-28T00:00:00+00:00", "2023-03-01T00:00:00+00:00", "2023-03-02T00:00:00+00:00", "2023-03-03T00:00:00+00:00", "2023-03-04T00:00:00+00:00", "2023-03-05T00:00:00+00:00", "2023-03-06T00:00:00+00:00", "2023-03-07T00:00:00+00:00", "2023-03-08T00:00:00+00:00", "2023-03-09T00:00:00+00:00", "2023-03-10T00:00:00+00:00", "2023-03-11T00:00:00+00:00", "2023-03-12T00:00:00+00:00", "2023-03-13T00:00:00+00:00", "2023-03-14T00:00:00+00:00", "2023-03-15T00:00:00+00:00", "2023-03-16T00:00:00+00:00", "2023-03-17T00:00:00+00:00", "2023-03-18T00:00:00+00:00", "2023-03-19T00:00:00+00:00", "2023-03-20T00:00:00+00:00", "2023-03-21T00:00:00+00:00", "2023-03-22T00:00:00+00:00", "2023-03-23T00:00:00+00:00", "2023-03-24T00:00:00+00:00", "2023-03-25T00:00:00+00:00", "2023-03-26T00:00:00+00:00", "2023-03-27T00:00:00+00:00", "2023-03-28T00:00:00+00:00", "2023-03-29T00:00:00+00:00", "2023-03-30T00:00:00+00:00", "2023-03-31T00:00:00+00:00", "2023-04-01T00:00:00+00:00", "2023-04-02T00:00:00+00:00", "2023-04-03T00:00:00+00:00", "2023-04-04T00:00:00+00:00", "2023-04-05T00:00:00+00:00", "2023-04-06T00:00:00+00:00", "2023-04-07T00:00:00+00:00", "2023-04-08T00:00:00+00:00", "2023-04-09T00:00:00+00:00", "2023-04-10T00:00:00+00:00", "2023-04-11T00:00:00+00:00", "2023-04-12T00:00:00+00:00", "2023-04-13T00:00:00+00:00", "2023-04-14T00:00:00+00:00", "2023-04-15T00:00:00+00:00", "2023-04-16T00:00:00+00:00", "2023-04-17T00:00:00+00:00", "2023-04-18T00:00:00+00:00", "2023-04-19T00:00:00+00:00", "2023-04-20T00:00:00+00:00", "2023-04-21T00:00:00+00:00", "2023-04-22T00:00:00+00:00", "2023-04-23T00:00:00+00:00", "2023-04-24T00:00:00+00:00", "2023-04-25T00:00:00+00:00", "2023-04-26T00:00:00+00:00", "2023-04-27T00:00:00+00:00", "2023-04-28T00:00:00+00:00", "2023-04-29T00:00:00+00:00", "2023-04-30T00:00:00+00:00", "2023-05-01T00:00:00+00:00", "2023-05-02T00:00:00+00:00", "2023-05-03T00:00:00+00:00", "2023-05-04T00:00:00+00:00", "2023-05-05T00:00:00+00:00", "2023-05-06T00:00:00+00:00", "2023-05-07T00:00:00+00:00", "2023-05-08T00:00:00+00:00", "2023-05-09T00:00:00+00:00", "2023-05-10T00:00:00+00:00", "2023-05-11T00:00:00+00:00", "2023-05-12T00:00:00+00:00", "2023-05-13T00:00:00+00:00", "2023-05-14T00:00:00+00:00", "2023-05-15T00:00:00+00:00", "2023-05-16T00:00:00+00:00", "2023-05-17T00:00:00+00:00", "2023-05-18T00:00:00+00:00", "2023-05-19T00:00:00+00:00", "2023-05-20T00:00:00+00:00", "2023-05-21T00:00:00+00:00", "2023-05-22T00:00:00+00:00", "2023-05-23T00:00:00+00:00", "2023-05-24T00:00:00+00:00", "2023-05-25T00:00:00+00:00", "2023-05-26T00:00:00+00:00", "2023-05-27T00:00:00+00:00", "2023-05-28T00:00:00+00:00", "2023-05-29T00:00:00+00:00", "2023-05-30T00:00:00+00:00"], "xaxis": "x", "y": [0, -0.00103031511424608, -0.02225411369267984, -0.04365877197954577, -0.00443096847928459, 0.004596225678043695, 0.013831582648696626, 0.0636326209687371, 0.012586816352474396, 0.011667871318938754, 0.011068862455640406, 0.010457414914399193, 0.009944552041724933, -0.0006955129088094706, 0.04935076181395188, 0.0086665166476834, 0.04885131129512678, 0.04844549504315056, 0.048360754315732765, 0.0278641705139872, 0.04765792452838879, 0.047483485247438895, 0.006885120666127156, 0.0065773358064507344, 0.06728929339867509, 0.027160189287791483, 0.05766701284060056, 0.06792851327474676, 0.04794427320598316, 0.04812343519207147, 0.05846168353564952, 0.05889505570360819, 0.04936802273045727, 0.06995277327067065, 0.09058485670745133, 0.07068136188051477, 0.09140012691405638, 0.07191570780765182, 0.1128437772218152, 0.11345739497296972, 0.09376872613223704, 0.05411402418428001, 0.11522063171013072, 0.07553547445598943, 0.116606645282804, 0.11736225414722914, 0.09803610364861257, 0.09887432401800714, 0.11009416844785494, 0.12129536028937157, 0.10213225867839942, 0.08289826179059638, 0.12429521395822883, 0.075123717998763, 0.08630149934176357, 0.10758143697342121, 0.07853824503620606, 0.15064786429146879, 0.14202131721263866, 0.11336418141003127, 0.15540238896109143, 0.13698868539646847, 0.09826958865217837, 0.14002294800747164, 0.1823137810144347, 0.20462270467497917, 0.18649717971561075, 0.22910298391180867, 0.1705786203539725, 0.17272890313478492, 0.17496573939735072, 0.2178120044518245, 0.16968829368917812, 0.20231195113589387, 0.2857096053129632, 0.22789226098989898, 0.2912212000383714, 0.2538532156227341, 0.17593141620825958, 0.1382802089297619, 0.1404552824367583, 0.14298559030415398, 0.20613300458629216, 0.1281028797144197, 0.13069675489495775, 0.09295298411143009, 0.11570842378060642, 0.09801377511872618, 0.08067284304059462, 0.08324596771151521, 0, 0.013095503224342471, 0.016209333619735047, -0.09152494951672963, -0.0886628944994327, -0.0856789334187004, -0.08268468913836441, -0.06923848619860969, -0.1471483642149475, -0.17500565804303786, -0.1823626487740078, -0.1089655430509003, -0.10614966719890008, -0.05267511559808476, -0.05968472602352632, -0.09684864793573555, -0.09374379085801521, -0.15100741187672115, -0.1579530957640469, -0.14496416274582144, -0.09110094165812735, -0.19863400450185467, -0.1553827673099835, -0.17278655148961147, -0.15987315158707266, -0.14685182122489163, -0.1436159865647551, -0.19093217390846726, -0.2385270142441076, -0.24620500243815735, -0.25372414415273403, -0.2713099843240039, -0.26891337770076723, -0.27608650159547854, -0.23278562520788085, -0.21960127392614792, -0.22659793615323423, -0.31453343156393937, -0.31167597463857405, -0.3086155621454263, -0.24481000338167375, -0.21146635387950838, -0.258512161555223, -0.2956915287827029, -0.3128951973222293, -0.20899698768702696, -0.1448924740333677, 0, -0.0970195835311491, -0.0527087671098458, -0.06888112655399324, -0.04476063293795795, -0.04047327344295912, -0.03644076971939266, -0.03199891045039198, -0.03782594684047816, -0.023555753186746135, 0.0010129623069652879, -0.014821176931084232, -0.010680480638247444, -0.016260463336507856, -0.08266228760121211, -0.017816861279895273, -0.013407668410080002, 0.031103965952287125, 0.015513478174940504, 0.06014152229645402, 0.021662638570656274, 0.06397006008076392, 0.055636011748163204, 0.06709050816088578, 0.12960321132824548, 0.15223779682814573, 0.13467634395166783, 0.13729706651273202, 0.20047046448349537, 0.1525998404905867, 0.20615070744764608, 0.20898717262975067, 0.2723731069340114, 0.22493359124315515, 0.23765370892512971, 0.2201228334980221, 0.22265599960241367, 0.2860118701432204, 0.30910286384855606, 0.31199906609945904, 0.27434764236005865, 0.3478469156180458, 0.2999950843614523, 0.31998201651428926, 0.2695579207514739, 0.3000260847064342, 0.3208064371994508, 0.26066501914642026, 0.3416990554189526, 0.26129036692763397, 0.2606682343265765, 0.25975566941350015, 0.36042758071938436, 0.4616890602271432, 0.46251266913816796, 0.4721112233617588, 0.42119936653536616, 0.4109788272247959, 0.40041296106764307, 0.3595819453108434, 0.34871002921137695, 0.3582926720369397, 0.34806198714313036, 0.3378748281261001, 0.31750170947772016, 0.3477768496721784, 0.4388705074107972, 0.4190720009206613, 0.3586169981570409, 0.3375709365673993, 0.33603702907096544, 0.29428677669091324, 0.2923976127178579, 0.2904424207142355, 0.288144536670826, 0.24557692173594195, 0.18254158039905596, 0.2202561293238962, 0.17683273290919924, 0.21503270161138854, 0.1622550166341518, 0.10952780287877412, 0.10684253103701503, 0.14397038061152131, 0.08060715935644848, 0.05634565458975455, 0.08085031418367884, 0.06625981431088887, 0.13291697019582685, 0.058551636930878996, 0.11537559761992434, 0.15262200422379268, 0.17981205596119956, 0.12660393388874996, 0.1643764741145608, 0.14149067804143667, 0.1188923928772853, 0.11665707348794067, 0.10420391418116738, 0.10182626509048502, 0.12976591348325872, 0.12774780792204835, 0.08509947202122302, 0.08277816322030851, 0.0804171369865186, 0.07857162200050617, 0.05644663551138432, 0.03439472852162726, 0.08325831326929956, 0.07119938848811284, 0.04892635158964664, 0.02661773372454417, 0.06521917348385463, 0.06385857756273104, 0.021926457792774853, 0.06098062081244916, -0.0006530224620688455, 0.01859721034213807, 0.007503069933821807, -0.003900483857977005, 0.004631830806571031, 0.01341007209747987, -0.008043645585975761, 0.021191507644482736, 0.11067222225928913, 0.1301646992811208, 0.11982646849902226, 0.12936345213246225, 0.05813649162609625, 0.04721175885647093, 0.06655858978464248, 0.06586142809578618, 0.10539628178476497, 0.08462344535595762, 0.10419400549132468, 0.10360671637733365, 0.12322916327615245, 0.1126348063743408, 0.06175296579465733, 0.11187928040142911, 0.10142567685270995, 0.10056483859211038, 0.05930614376392785, 0.0778570409618276, 0.10672238404529884, 0.05521176306645261, 0.0939270985294421, 0.092468249384312, 0.1314634373730041, 0.15041937393346036, 0.14938809890424545, 0.14816738035285176, 0.1874060964320026, 0.16645763396630614, 0.14536849200437335, 0.14461334654176367, 0.18404148532958656, 0.183465240191123, 0.2027801075278742, 0.19181763784164912, 0.20058041779023256, 0.17978738087098808, 0.1387371062273113, 0.137927371983454, 0.17724749632765574, 0.17628797248634, 0.13482729756754028, 0.1740904377521077, 0.1326481654165087, 0.13139273109061145, 0.18072422304165517, 0.16970335016051266, 0.29006033974554146, 0.18831097919338344, 0.2277852297737664, 0.14618494556680703, 0.18551608482022044, 0.20470130913425696, 0.13267347137786165, 0.14143234919435654, 0.2006110127933971, 0.15912452521594594, 0.1376434345444156, 0.13605720762035142, 0.1344024046881439, 0.1330122168567083, 0.1518509364889706, 0.17056702868247622, 0.14901143012621332, 0.12755581859660955, 0.125998055271423, 0.12431928982112232, 0.16355665545775308, 0.10157554928231886, 0.18072638822329784, 0.11884285696255178, 0.1171201386840276, 0.15598297578330778, 0.15424573472639122, 0.15266836498285982, 0.09063511759835094, 0.12925489177883484, 0.18832861602154088, 0.16643339659030743, 0.17489376944906687, 0.1835207472656533, 0.18196666665684444, 0.18018809708415398, 0.13792516620768616, 0.13603180138514576, 0.13413731298447407, 0.15259425514454814, 0.1506992086091751, 0.1283417872960518, 0.14657856373971898, 0.18516520498141328, 0.20379417707330466, 0.22248144314523816, 0.18061511257686033, 0.21943958018876955, 0.17752571897686606, 0.21613718754418748, 0.19452962024038978, 0.19289147766158493, 0.21127866974319826, 0.20959770095085092, 0.21820878782132813, 0.16605258300148545, 0.20653474843881697, 0.20558553471943347, 0.22469293471295312, 0.22349374427716143, 0.24268842725574113, 0.22160946536039267, 0.22086943677188256, 0.22044925706282223, 0.2600549409815517, 0.20889794893532224, 0.21844807448690176, 0.207859757073427, 0.21749669945907366, 0.21700321939704284, 0.21683074207283554, 0.19635391717896597, 0.26683372054500815, 0.2161882808840066, 0.25645183622620515, 0.27630043840796475, 0.2559251899886274, 0.25583911827323774, 0.2555755182365914, 0.20503273502205624, 0.2155308744301497, 0.21573279477328658, 0.25640555426922784, 0.2566956673938101, 0.256918896714393, 0.25718606497170826, 0.23742234011862196, 0.21781320254566613, 0.2586935796584873, 0.2793801054026504, 0.3202362418797603, 0.28028212892442517, 0.26083006139300674, 0.30181900833339637, 0.32271655295975077, 0.34354180096567644, 0.2836744839642381, 0.32460715994217465, 0.32543225565843253, 0.3260771958641157, 0.3471117464216279, 0.32782394594740477, 0.32870339698984874, 0.2893631486462987, 0.31073101982132006, 0.33208341553096316, 0.3331234957390083, 0.33409205139836684, 0.29506111454919504, 0.3164039115264067, 0.3377295849031291, 0.3188329006720988, 0.2999183695821896, 0.3419306421379397, 0.3031816401035878, 0.30478464074093004, 0.3066451876197722, 0.3084299734207742, 0.3603511663018067, 0.31160414077461496, 0.3137134007920589, 0.33609678631431483, 0.3180304143748033, 0.36071903300230534, 0.31229654376644667, 0.3044421486875258, 0.2866098271963925, 0.3194666398682279, 0.31164395311152915, 0.31413775420929585, 0.32716902446206153, 0.319704131248904, 0.3025894159822689, 0.2853880506903733, 0.24775101418435969, 0.33090996252107185, 0.2727437100469441, 0.2954443863191302, 0.2781346707456188, 0.26050795850656955, 0.24301095923593943, 0.24555124525949729, 0.26834988392012316, 0.19029614131730446, 0.23336797619497146, 0.19571215077595017, 0.1683553003982038, 0.16136736317606495, 0.10411578352021789, 0.10699246127864141, 0.08975310142345964, 0.0927603709886873, 0.0353894288430777, 0.11931388385923154, 0.13191182175320892, 0.05371003430494253, 0.06648159035392598, 0.10978737437968766, 0.09248430743040234, 0.11583237344973349, 0.0786251923770467, 0.07161589081705662, 0.024401426692554854, 0.007319785746935722, 0.05068969995163421, -0.016831540309768068, -0.023293206613842148, 0.019997528072986655, 0.06329394640079639, 0.04585654622038162, 0.03864111016107214, -0.008724528875379025, -0.005502677196346267, -0.002568450014760812, 0.04033006591476769, 0, -0.01762317062149007, 0.005007330832793121, -0.09327305341898173, -0.0904171325395656, -0.08735531741253555, -0.08425487397450473, -0.08122764123242293, -0.13910332967618022, -0.17653996780075742, -0.19364936039409578, -0.10968415355521212, -0.1669224539722308, -0.17381017175666852, -0.17082169157658472, -0.15790241008853434, -0.17471036128919987, -0.2112199491830984, -0.22730698039548428, -0.26399295466555894, -0.27006949187227625, -0.25610750133578886, -0.31257119069322725, -0.2680918676859235, 0, 0.02458384519877051, 0.038860988554576405, 0.012703974661077252, 0.017081450411618484, 0.04150496367200079, -0.03522258182906237, -0.030808031732700046, -0.047032033818999, -0.042654834030509514, 0.02227573199688001, -0.05435747235086656, 0.010695067174115004, 0.4391524881321514, 0.4206326108724501, 0.4629837696840199, 0.5252103390017845, 0.44570195835431387, 0.38712517052724843, 0.3491337138997175, 0.3314007054517175, 0.3541130970945385, 0.35679510133381426, 0.2581478902146664, 0.32186665247538976, 0.34479292486675744, 0.3072829847480977, 0.35045287771145356, 0.3327954229280476, 0.33527401245339555, 0.307387693579809, 0.3203397818716858, 0.3231802441539119, 0.3259541794056369, 0.30823504932295615, 0.3515542342394756, 0.8386732446722728, 0.767696940506261, 0.7169588747367414, 0.6565035178842376, 0.6570186050202167, 0.6773264376106954, 0.6575083622070835, 0.7786787386944878, 0.8183078190546151, 0.7364617014040294, 0.7161428993327597, 0.6661263338624198, 0.8991019559623173, 0.8581041351044245, 0.7563806143510476, 0.7964847977192124, 0.7554454950776015, 0.7749400377859305, 0.7133950127924605, 0.6319803503121089, 0.6013839351949848, 0.591030568785953, 0.5502531958395072, 0.5501964934085188, 0.5505730769896893, 0.5507707621443301, 0.6717551632075648, 0.7717056481094972, 0.7094308308770819, 0.7684045665901917, 0.7663769871719558, 0.8147768434174661, 0.7618492000933418, 0.7493043557322858, 0.7566215260007962, 0.8145360061335103, 0.6395466032864705, 0.6474549520307413, 0.5741799846068472, 0.6120435443096734, 0.7203256686598184, 0.6160739850927772, 0.7947708655834057, 1.0934918468534127, 0.8553890821288738, 0.7800488767196382, 0.7155383041807957, 0.7014146535482451, 0.6270338580429988, 0.6441160801939223, 0.5602143230550171, 0.5168319110668947, 0.5344394568812277, 0.4507871786508767, 0.39772571225488257, 0.40549325619142623, 0.3929115428016507, 0, -0.052635218537505406, -0.03461300577255459, -0.05720452228297477, -0.059603441549764005, -0.16264058748830618, -0.1645614211840113, -0.20687185040509565, -0.24910815066486536, -0.17015734704109892, -0.17222393438452682, -0.1945609291987768, -0.2571660881781502, -0.31925087605272306, -0.31061975870690306, -0.36265720009438446, -0.4242869363478218, -0.47589451064497, -0.46678010889607086, -0.4276291713934026, -0.38878673398388963, -0.4306244330710808, -0.45209476022344575, -0.49371985672569707, -0.3840753845647202, -0.45565785097748457, -0.5368368774890839, -0.486866451899588, -0.5176335488514704, -0.5184681043095298, -0.5193866342596467, -0.5403433826907165, -0.5008551259176071, -0.5418776697327332, -0.5425755416791064, -0.5431900287937343, -0.5438318070553162, -0.5544483245356071, -0.6053333289333216, -0.6260622000789895, -0.6061960673496677, -0.5261605625970115, -0.5471946671230035, -0.4275165942361139, -0.44926733901151716, -0.5311321407620248, -0.43176103779170316, -0.45347505635602103, -0.454981295217487, -0.4562892426745107, -0.4876924160123977, -0.4585859580870477, -0.4598511765706194, -0.5212827917553022, -0.5221484202406755, -0.5633879907247222, -0.513881916358173, -0.56505988242659, -0.5053117169164343, -0.5061718995435202, -0.42662481641748606, -0.5688176532496422, -0.5896330298691698, -0.5904619733241664, -0.5309518320942193, -0.5319298829987935, -0.5329266208283594, -0.5341634335072944, -0.5452588515178939, -0.5364136722267845, -0.5377035461426628, -0.5588578413731181, -0.5397242269666779, -0.5200604992227474, -0.5413119062995205, -0.5723128668816743, -0.5633651983877828, -0.5442004910084686, -0.5049416298738004, -0.546774732297588, -0.5078773558275556, -0.5493505424943337, -0.5506034941302539, -0.5117466059668926, -0.5032955279590949, -0.5552704375760282, -0.5566838976403882, -0.5177640164466114, -0.5393165509763275, -0.5810003659619514, -0.5421081729879551, -0.5235680413997931, -0.605740120649814, -0.5665391587619301, -0.5279085493741484, -0.569531688239706, -0.5308056186320153, -0.5526589254545357, -0.49370706333155867, -0.5256390278367057, -0.5775233396367676, -0.538938351805682, -0.5405916921657302, -0.481550504153795, -0.5034431704241427, -0.5251727010231734, -0.5064971453193553, -0.4678177414524761, -0.4695993397441766, -0.4917206782811891, -0.49361484076964485, -0.5156297590418576, -0.47706405493556264, -0.438715086424522, -0.4811799151033756, -0.4830683836622835, -0.4947907807122236, -0.48622670468856133, -0.49772036004004616, -0.48925923198899557, -0.4906856199019773, -0.45208712126884026, -0.5040244240738658, -0.4753102851426519, -0.4366974079232512, -0.4484843711348055, -0.48032909666111745, -0.4616441888580945, -0.5137594716285215, -0.45470865631797774, -0.4865185594457844, -0.5079830951797071, -0.4689643551268382, -0.4300496647201551, -0.4115732918099735, -0.5340337144246745, -0.45462505098179995, -0.3653556659415178, -0.43781909316460116, -0.4192804062783708, -0.3807732756401219, -0.46314238760372445, -0.42432868744232444, -0.476262713388555, -0.3670486278149715, -0.3687058035586273, -0.43098924829767316, -0.3820311439346906, -0.4137044127882805, -0.3849456132122777, -0.4267550268477453, -0.43814441446272023, -0.3390660570728689, -0.39137770065106814, -0.4232669351686295, -0.3843263759524832, -0.39588972102993353, -0.3872510361665653, -0.4893152400939315, -0.42993970248438407, -0.3911090500463968, -0.36231706566395877, -0.35367330743107506, -0.41526335007814313, -0.43647634867020085, -0.4576682066504988, -0.3577700085808643, -0.44944242187409045, -0.419751045952156, -0.440836247940998, -0.44144187855622485, -0.36153805072041045, -0.4430168238245997, -0.3227107916027238, -0.3842324227588657, -0.4051563575295819, -0.38592191496118367, -0.36665941891748827, -0.3473724198746299, -0.4287637222893101, -0.3688415384201816, -0.36980685730876633, -0.37063204483825135, -0.35119995542599913, -0.3822559059299227, -0.413226490283073, -0.37335828241348457, -0.354027724638584, -0.4152527082329344, -0.4358767820957121, -0.365817701849606, -0.3763175332990391, -0.3769494113717507, -0.3775498832320442, -0.3578910118209845, -0.43901815019643947, -0.37860704755081964, -0.43949825070654075, -0.45983893553065885, -0.4700892229505176, -0.43984047190017644, -0.4600852159570492, -0.4801909443767503, -0.4598492363157899, -0.5000118295941072, -0.5299656074225336, -0.5195946080206101, -0.5597651695557686, -0.5288222798097092, -0.49834680658193087, -0.5986707044415148, -0.6180448805325383, -0.657512872347151, -0.6565941889083567, -0.5852207349261299, -0.5643981022096327, -0.594399700641726, -0.6340457962167242, -0.5328208872858841, -0.5731282364514703, -0.5830566652187097, -0.6535987919169676, -0.5929328449386403, -0.6332883662616969, -0.6331512308428611, -0.6534417694018856, -0.7334994016273072, -0.6420162061362512, -0.8029459382252101, -0.7113973473775106, -0.7106425914519647, -0.7904571951755303, -0.7087158929729028, -0.7282195058090075, -0.7073986070685774, -0.7875458368361853, -0.7868465202542936, -0.8462569545133984, -0.845337902642237, -0.8243271886793802, -0.8534795545228244, -0.781622184884421, -0.8410134935966086, -0.8598612460718604, -0.8989752867404264, -0.8573604587269029, -0.9165510707895449, -0.8745526845265448, -0.9134654328909573, -0.8717541978168564, -0.8502562705466029, -0.8897639282783821, -0.8284284472386718, -0.9484870527183251, -1.007493622509038, -0.9454304985951898, -0.9242500777502698, -0.943623930439642, -0.9428575252439919, -0.8812623372402741, -0.920924618860781, -0.9603941430827098, -1.0398688619135124, -1.0182191742221758, -0.9766605478098239, -1.016003261794982, -0.9948636092351335, -1.0343981484807228, -1.0335084399047931, -1.0325471119732688, -1.0721400444013287, -1.0710362830727465, -1.040181337484119, -1.0496042195912658, -1.0890843069343707, -1.04801124897245, -1.0373283556312733, -1.0366340798749691, -1.086668952062416, -1.0358127411314004, -1.045688904319135, -1.0454372508011667, -1.0347807193372438, -1.0849677422917507, -1.0238731485770936, -0.9909453842828663, -1.0287068988858725, -0.9352305191517226, -0.9525717259550487, -0.9299940121545468, -0.9078541380839096, -0.9061548428625754, -0.9045579964128745, -0.822355556341397, -0.851258826365849, -0.8000560181286731, -0.8493983398835913, -0.7876800176244105, -0.756692609842442, -0.7256435581720445, -0.7149863593620029, -0.6540749047283373, -0.6540186689241377, -0.6740219043789101, -0.6536670277660316, -0.6937795181112963, -0.6331718090214958, -0.653152246141195, -0.650235379463506, -0.6175190838576261, -0.5651294027834054, -0.5032760890244836, -0.6027183687712431, -0.4499972294039321, -0.48934969548159307, -0.4775174461680286, -0.45545539719573735, -0.5345467595753705, -0.4724678364724674, -0.49147403041083354, -0.46886143356864673, -0.47675808657389906, -0.6360593055219975, -0.482389008752741, -0.5309825589553678, -0.4785844262242056, -0.4771041825058126, -0.4961928503261632, -0.45498360104882873, -0.4744112793901173, -0.6146836993379682, -0.5127371584756286, -0.5320793379700065, -0.5714263217596945, -0.6099049561067607, -0.5268906327182404, -0.5452564442327791, -0.522953716156659, -0.5611218707001279, -0.518333662199407, -0.4760433738994571, -0.5044698069153455, -0.502503256311438, -0.500216565188111, -0.5592435353003956, -0.4770584736270297, -0.5365599291405871, -0.5150344943318171, -0.563453256823533, -0.6524020899672809, -0.5789083264756167, -0.48403598387276187, -0.551917399753746, -0.5493969586645349, -0.6478285158858204, -0.5647025927083865, -0.5629081292707622, -0.6012657838414839, -0.5795049577489244, -0.5782198841681033, -0.49636381112986494, -0.5562628248684731, -0.4954051635911115, -0.5153270449250682, -0.5150999588581644, -0.5752623952321002, -0.5247230969912866, -0.47438128018948017, -0.5250430800593785, -0.5251551144914532, -0.5157824447057191, -0.5266128930781193, -0.53763248435703, -0.5387786302802826, -0.5797451465640115, -0.5199045423523025, -0.5309576189235452, -0.5523678137793433, -0.6042039273496272, -0.5651830240180612, -0.587043535773159, -0.5890248201144126, -0.6315439585887767, -0.5732008185251153, -0.5954694144809762, -0.647602018924049, -0.7198130631085456, -0.6509298023694917, -0.6832681575715097, -0.5639460126796043, -0.5764706622161783, -0.5692802867680542, -0.511581212744337, -0.5549852583290449, -0.5780696352772849, -0.6211704537268646, -0.6138662619553207, -0.5158669563900923, -0.5393681722444508, -0.5325938381789055, -0.5258730304651085, -0.5393370446432332, -0.5326802592795687, -0.526358771227083, -0.540329726043321, -0.5138895292804914, -0.5374753484029476, -0.7019048232467812, -0.6233273160810293, -0.585550280742517, -0.4880722427114471, -0.5113059936108791, -0.5546585419500342, -0.5172997302728941, -0.44997961667602016, -0.4838010914546373, -0.5273387709985283, -0.570648042291845, -0.5335737615947432, -0.5872540004808668, -0.5402578367696285, -0.5837924958768888, -0.5066465629399716, -0.5100740560781641, -0.5939632095710751, -0.4757512768356128, -0.5400362394460311, -0.5235998971542424, -0.5676112280307344, -0.6110496147314699, -0.5536429647599759, -0.516661298184482, -0.5704172917743062, -0.5434460370594584, -0.5668581358466168, -0.570010284056865, -0.5732440071890348, -0.5964535225188015, -0.5389349383977524, -0.5220691027015023, -0.535622621607682, -0.5087934965739421, -0.5725221930440023, -0.5753022733206209, -0.5581464048371501, -0.5408457793352062, -0.5540143453122264, -0.6073294352437496, -0.5896847201928587, -0.6122958291887829, -0.5646106678843852, -0.5574013943935517, -0.6606368643787317, -0.5621927004248946, -0.4845886185069743, -0.5077678030471658, -0.5910406942855491, -0.5937830129828038, -0.5761383428661433, -0.5583298775703066, -0.5911040252648138, -0.542960530676792, -0.5454536232549992, -0.5878036501309304, -0.4891966151938155, -0.4819166446872036, -0.5951406933522817, -0.5970342954011347, -0.5582985450166581, -0.6103810072391251, -0.6722994633011837, -0.562701205550166, -0.5540298707289213, -0.48499418535262473, -0.5471860033748921, -0.5385660610383526, -0.6306533463843459, -0.5710610708074252, -0.6724784245132431, -0.5622415820700375, -0.5732789389752246, -0.5741937641943524, -0.5749926857106336, -0.5657239991110273, -0.5765189364276967, -0.5368896238298261, -0.5575649800644734, -0.5582242955044137, -0.5586555850698642, -0.5990486832446357, -0.5386022467878544, -0.6194456476990947, -0.5185784928741427, -0.5190882662143148, -0.5393240908020281, -0.5193116797237398, 0, -0.05059692149883188, 0.010595115294490741, -0.009260632787033445, -0.059952569008152864, -0.008735184837240546, -0.028018009660790294, -0.027293771560582703, 0.014047258130559346, 0.004896052886274116, 0.03585976155060762, -0.0038372585614592767, 0.007024434613233483, 0.008393783021413557, 0.019933164719042967, 0.0008123530649902771, 0.022580685625579168, 0.044133271170479764, 0.04557631868642772, 0.056968491297754685, 0.02785557099900091, 0.060199464443779695, 0.01125926342352967, 0.07371178446243368, 0.03499362167716596, 0.05717301898163737, 0.0490098956347764, 0.07073711690291645, 0.0826271050736781, 0.03406987949862562, 0.0871258644686914, 0.008561952854328491, 0.04138545777711262, 0.013313210962304589, 0.05612372427762032, 0.01843001281606398, 0.08162087945087672, -0.017102143004687615, 0.036351261272300345, 0.008676701611402403, 0.05244115871975312, 0.07535345949092997, 0.1696513082004951, 0.060925759517791854, 0.04374429189221029, 0.12794130790778008, 0.12084253107285615, 0.13416957129557344, 0.157146977651486, 0.08937918009733965, 0.06248493509726962, 0.08635030175075388, 0.039464328126246416, 0.05339772015850595, 0.057026061758718265, 0.06090776817200445, 0.06493022560444726, 0.06864663093401903, 0.05205107283995901, 0.11688577383321087, 0.1409000695842061, 0.10416307816260437, 0.10811521381789552, 0.09197635454204159, 0.11597102808281244, 0.05930860271220345, 0.08356300557800717, 0.03682987562327573, 0.09145009917695156, 0.09541570773718192, 0.058835451364604455, 0.06282609742736918, 0.10738652242132261, 0.12117713111678574, 0.05385830389020846, 0.057773869286340894, 0.061472875446470156, 0.085564102283416, 0.06927521233774474, 0.032315863033088484, 0.025807314470354423, 0.0595760499904911, 0.03250991794938339, 0.02622336682685082, 0.09087895111623143, 0.07413835383102223, 0.05728175996325259, 0.051201827503606176, -0.056167273457651004, 0.008905019451755416, 0.08335755761849557, 0, 0.003480015810876322, -0.04357626781053534, -0.03995532877114609, -0.0367943429995485, -0.07356853105609182, -0.09081332180243143, -0.08725689926104538, -0.07361595172440691, -0.12112282893345781, -0.1275099285139096, -0.0632365391390841, -0.1507878693236224, -0.11704185090880868, -0.16433285389479393, -0.17128100530859391, -0.15799394372588957, -0.15478225175124083, -0.15176633917521298, -0.14839044245438282, -0.10495862105619021, -0.10231286472856538, -0.09992200425012125, -0.07712720892414725, -0.09523977797005542, -0.15346548537966018, -0.13078152909798454, -0.1282284690914053, -0.13570390897544113, -0.10296702012548835, -0.18125210775690861, -0.19916799316613756, -0.1365126823179355, -0.17494514747550152, -0.18276702399237893, -0.24121381285705104, -0.22863088654404531, -0.286554500655941, -0.2534586136126146, -0.2612215997884529, -0.4209873671748068, -0.29686465992121547, -0.20354771656664844, -0.2322636850800895, -0.2910589250485362, -0.2891643924715366, -0.28731900922598197, -0.32594394510524577, -0.32419856401985964, -0.3629246173393283, -0.32047117543302556, -0.35918924700577887, -0.35753923153424655, -0.4365607838785083, -0.3938693777919478, -0.3716691721532468, -0.37012382303122976, -0.3683554469169825, -0.3869089042789714, -0.4357209638336493, -0.3933154483407894, -0.42220259814365624, -0.41022225709355487, -0.40829622765707707, -0.3965449440695755, -0.3136776220911384, -0.3931018024918227, -0.3911926044272398, -0.39934042440381523, -0.3367564192237059, -0.3351996542412769, -0.2423158158340378, -0.32194888766813046, -0.19882272261420353, -0.3192229318766955, 0, 0.0016792515590666828, 0.08452219282805168, 0.08584639281682772, 0.04691002981144015, 0.22091792565580418, 0.18100998490319192, 0.21229629268396683, 0.15193097636506614, 0.11242633251692621, 0.11350177500135804, 0.13477888516221484, 0.1155022765574548, 0.17762068061689407, 0.3004572927928274, 0.19907684809256074, 0.18008786641215674, 0.16091300445605022, 0.1823677895883667, 0.20374768792056253, 0.20423550265308418, 0.20498683108902035, 0.2562190612727891, 0.30721211669045373, 0.24633889074814463, 0.3064266133150821, 0.2149567126200692, 0.23509429495151649, 0.2452004965896486, 0.30583973512038, 0.3659688843353081, 0.31444416890203086, 0.34408581852824743, 0.34230638576354255, 0.30047856354406555, 0.4005578996725677, 0.3174003194623714, 0.33572193667014566, 0.35431057312406894, 0.3523420798019795, 0.35041839769670763, 0.23673779833280462, 0.2656938134510883, 0.304779332532423, 0.34340532183011185, 0.3412109322685817, 0.25788333521227214, 0.2860756359174102, 0.2942182754745201, 0.2715029351037795, 0.3097569943380625, 0.26637483158030817, 0.2935924958017809, 0.2399053793586982, 0.2578766179182785, 0.21448910446486258, 0.2320680263842367, 0.2894585114064446, 0.24556649515385312, 0.2525164258933268, 0.23886459922381886, 0.235464138392688, 0.23201339925755454, 0.22895998135492568, 0.18499031158501147, 0.283400436955382, 0, 0.01676201092815879, -0.027623823791274277, -0.05132957072647171, -0.07468766912690628, -0.07803602040754108, -0.12185695268963663, -0.10478841833885782, -0.10814343467369263, -0.09137730926126957, -0.05453690723117055, -0.048502357349892375, -0.022005145131690457, -0.05642153539969855, -0.1918965064125932, -0.17533069056269313, -0.11824680830201376, -0.13239425515649703, -0.1566241336533498, -0.17088042019838545, -0.13423223152604202, -0.13837405912822037, -0.18324898090523228, -0.10701125532124578, -0.17236756769579994, -0.11627430436314302, -0.12051405358338337, -0.18547546975682622, -0.10853881730119876, -0.24401461902290064, -0.17682832634201637, -0.1303645977274318, -0.15517695516649382, -0.20004862706617957, -0.1936044936615361, -0.14689344335781818, -0.2116113782730333, -0.2051818621132731, -0.2089734800090511, -0.2329251368351398, -0.17584557388274194, -0.23034876381423885, -0.284404646969262, -0.1462209142970965, -0.27253890192704444, -0.21577882767695095, -0.23999201566906153, -0.20287500792924074, -0.20651958323889374, -0.27102751656651786, -0.20355599748685677, -0.21694489978048645, -0.1596632541118892, -0.22424071582698152, -0.24825170870750599, -0.21125430675265983, -0.1747447542772626, -0.1785971281424109, -0.18265261479823516, -0.1458132779005167, -0.1698173640567626, -0.15370408003858407, -0.24819937253601046, -0.20097977622546237, -0.20444813708062798, -0.20811196227695775, -0.2119701543816454, -0.17503621332122357, -0.23914657601494488, -0.17176573618599705, -0.24608994159331946, -0.22912657089019273, -0.15158958229312447, -0.23619926042045608, -0.21935540142350557, -0.2831156514325865, -0.3059634934086666, -0.2478363700029985, -0.250494351851129, -0.19279518445767968, -0.2566889845257876, -0.2594441480685688, -0.24183017701273893, -0.18389886264437025, -0.2680575316553465, -0.27074471687656887, -0.27349636476655964, -0.21527143532806525, -0.2987952438314543, -0.1802081257005269, -0.24364482611249622, -0.3062393400929193, -0.23715579694751918, -0.22950211653188826, -0.3330488725751058, -0.21348291082226986, -0.21572439057549503, -0.21807820340141865, -0.2605343054826589, -0.30270789220907657, -0.21341727284731687, -0.20487846030917764, -0.2973726063681359, -0.24839771675770206, -0.22996807848136525, -0.2315861863687666, -0.25364140565503046, -0.22531613406150167, -0.2369170254574451, -0.25845652290220533, -0.1985650088188983, -0.21003761498357526, -0.22133679942353837, -0.20269776769849032, -0.2646376288444413, -0.1852182085746632, -0.20665440586932932, -0.22796697807537628, -0.21878247689586955, -0.2095889810403269, -0.21043760950961915, -0.21127672585879498, -0.23204182480173474, -0.2729633361267809, -0.17207374801887526, -0.11211127964381891, -0.21437639270916495, -0.15394711852009704, -0.1548813173295306, -0.17540592294078022, -0.17553807312208972, -0.11506429467541876, 0, -0.06093004492630033, -0.06101668940563632, 1.8889116497367222e-05, -0.04052760087858049, -0.04057976941159568, 0.010319632395122538, -0.06055264849452706, 0.010718671967563041, 0.0717710830008533, -0.03955378632687252, 0.0017815402622788344, 0.002254703949099682, -0.017391213034749727, -0.04697802971315485, -0.015555307943555271, -0.03494716318682539, 0.006673847126841224, 0.01788841093905371, -0.0012519050962228348, 0.03044518656562663, 0.03150791677979338, 0.08340357500049994, 0.01335676766991025, -0.025537835571043378, -0.04412442629815203, 0.01853224711770527, 0.03026260656719076, 0.03198737629676052, 0.023706028696452636, 0.03550720497503531, 0.027345851666410313, 0.028976043195120975, 0.030832201075699728, 0.05282455511069062, 0.09522365368615905, 0.05626817649303188, 0.0786512367996883, 0.059938396785856704, 0.06191290641385882, 0.08401660687029719, 0.10628633412346389, 0.047640346752570326, 0.07085731204254142, 0.09339788340368005, 0.06516161354691706, 0.0983178247929889, 0.07057303597632869, 0.12407024698135394, 0.08636038421516844, 0.10925527786060446, 0.0914355319254557, 0.0738811961571908, 0.09669561124339324, 0.13973656789333372, 0.14211568415417639, 0.10405526800789397, 0.18770519180729697, 0.16974769759287592, 0.15183525304168946, 0.1748844856059175, 0.15749821032174324, 0.14003531199785077, 0.11259917300292444, 0.12574250506616294, 0.10860973404614221, 0.12165702432029749, 0.13479438500910693, 0.17799090185606106, 0.12006486909728047, 0.08275043856710715, 0.06571203153810261, 0.1297729131287574, 0.08242908387235312, 0.05568900893542704, 0.08947302415877685, 0.10287410320664872, 0.12636689824571462, 0.14988356534193933, 0.08211563735361066, 0.11574019607677856, 0.1390440001207541, 0.0814618170614833, 0.02406595781483966, 0.06816630911761125, 0.09215758540812345, 0.05522127182777455, 0.05889022348724582, 0.04227567618029779, 0.08589241705465848, 0.07903468004338023, 0.07263373698587319, -0.014779133446557867, -0.00047857488252613464, 0.06410011726419776, 0.08786754969942975, 0.040681646607742386, -0.006436759789770258, -0.00278721314998173, 0.021101739658835522, 0, -0.05729681184567855, -0.05359083555544715, -0.03943069935155128, -0.08655733609758876, -0.08263951266482492, -0.038525406368230465, 0.015425189271456028, -0.08266104133531023, -0.06887773525978817, -0.014675298150855743, -0.0619691928306847, 0.022795860925244767, -0.054830223409113546, -0.11177628370425674, -0.04685635309074125, -0.053235168140383975, -0.05991048555872085, -0.015841779827244672, -0.05297022831507091, -0.008997826776849502, -0.035939018182682324, -0.02270460258397391, 0.0207016313902298, -0.05724448115623775, -0.05403490726598759, -0.08126646214391828, -0.08845224338183696, -0.08543653064700142, -0.19410948881033732, -0.07938238024039511, -0.08680241617031781, -0.13557157405223652, -0.21378036839479775, -0.06843284278853679, -0.10623223980483036, -0.17454102084475404, -0.14129287927866918, -0.17908489761682933, -0.09547527834882387, -0.11355649153489256, -0.11154374529202368, -0.1599469374608599, -0.12737642408324334, -0.16585012826552953, -0.16411121063983333, -0.16219128989783396, -0.1096030728201584, -0.17877607915276564, -0.20712153476961107, 0, 0.02129060972696717, -0.0485128087874468, -0.06748904128563672, -0.04583656525905728, -0.06484919895087994, -0.04323391462993596, -0.04158453888257441, -0.060682769378120956, 0.02156618943601518, -0.058795042920595814, -0.04726685781525111, -0.03584340210115561, 0.006365292175336425, -0.05342892901818648, -0.09201536702980811, 0.0005726446611757071, 0.08270384652646925, 0.012404407697274219, 0.05431235370395905, 0.09593744714083631, 0.015523005341645926, 0.03774226929796644, 0.08995401847884821, 0.14200070079681285, 0.18324917923288178, 0.20374379696018796, 0.32536040432080626, 0.2843055992417173, 0.2635701918638252, 0.34450745131775695, 0.30406972121755427, 0.2632553768312811, 0.3035137069176274, 0.26236960570977236, 0.24175691188180734, 0.22131433223253769, 0.21105709311675633, 0.2008026092129004, 0.2617368661184326, 0.20100966887369515, 0.1807481652499076, 0.1811373221563625, 0.2012284715171379, 0.24152777404355838, 0.21069964872719196, 0.11955211484551997, 0.14060253963530253, 0.2118506647824503, 0.15115051423916656, 0.14137722158253732, 0.2124088821153535, 0.2025244434990269, 0.14155783964650756, 0.1518792594593617, 0.16220269739423476, 0.15216288612628504, 0.14212497111459774, 0.20237052484337442, 0.18136906900520625, 0.16064901816797222, 0.20098286395952578, 0.18026783480506975, 0.1490380478264836, 0.22950438912087556, 0.23862446774246276, 0.21714446677489319, 0.15542463799792122, 0.15454091139724382, 0.07275085048728895, 0.16325172246298347, 0.13177379681112042, 0.09005856335075868, 0.12965752393191793, 0.19970221691482776, 0.14753840651612976, 0.08553700949565332, 0.11491822315492865, 0.06306098103802552, 0.07219880569983929, 0.09136321682900614, 0.03924564542318835, 0.09932784273145455, 0.07777869371613476, 0.06653646330165301, 0.09559126039889794, 0.053425519486828166, 0.07209033542754675, 0.009576149643029535, 0.0686659059022961, 0, 0.028907395268522578, 0.10836592337032923, 0.07612094890891181, 0.08457800199628339, 0.1235005719707852, 0.24304103841480637, 0.06734260457175463, 0.09528553455719255, 0.13348643010342662, 0.13084928504075014, 0.12806330111868755, 0.0644016936735907, 0.1228293889644421, 0.09984536908383543, 0.15775765805927688, 0.07391917672148268, 0.10159743302110089, 0.10914910735869404, 0.1267473046228622, 0.12360660814530519, 0.16141213185771275, 0.08679148385358275, 0.053453771502283746, 0.050927874462609615, 0.06852710261834793, 0.06550419158361855, 0.0017297262942078473, 0.039850454355435036, 0.016607236626823025, -0.006486218485232203, -0.009230242155170378, -0.0019765095005070043, -0.07551251889019157, -0.00711444035838381, -0.10150698274481741, -0.06292097026345783, -0.004989441256162895, -0.03824818519878298, -0.010682054973114108, -0.02359456026930271, -0.03646394477672261, -0.019236406920902, 0.018525604209644764, 0.015473638848468611, 0.03300601182327073, -0.010696002196062962, -0.054293836977036936, -0.01640975423848166, 0.02146860473869143, 0.05912270954943278, 0.05602474872166014, 0.03288452395403335, 0.11105811581826482, 0.05718396710224727, 0.1044022928695304, -0.01048190701285506, 0.09814005048767523, 0.09470539079806188, 0.14192470113989245, 0.12830955475075848, 0.08403719867915774, 0.1315529837631674, 0.036581475343460054, 0.023274683327434216, 0.020613035599720685, 0.11882367112077492, 0.0646408556848588, 0.061065080796825, 0.07764990970364538, 0.05395570832740012, 0.08103915284683472, 0.0880405200575565, -0.0066316281734783876, 0.0005253811816412785, 0.03814936660033152, 0.05563908677148444, 0.03207482815255418, 0.06942012106453398, 0.06604833681775624, 0.07316881867388585, 0.0392872737939625, -0.02412269764179036, 0.05431945207589815, -0.03007599886040644, 0.007961095784867025, 0.0458523741413912, 0.08300416001113393, -0.041465722550295486, 0.05764504135517133, 0.01439726518188274, 0.05223269613522393, 0.028868732208043247, -0.04450021576092803, 0.004183853096488842, 0.06225620562261758, 0.029127328378684245, 0.05710688306792181, 0.07482480465258855, 0.03157193437225456, -0.011249777823686576, 0.047449190181823496, -0.0057018464034544036, 0.07351126056361378, 0, -0.002028410923194655, -0.024159203374167396, -0.005951826614618873, 0.0328941073668134, 0.07144741656221368, -0.011829202566195409, -0.013527214638581387, -0.015121725774009854, 0.03397568821733608, 0.022071920121663445, 0.020288827368090245, 0.06897222623820055, 0.08760356282296021, 0.1470256120143246, 0.09418394995905001, 0.03188483216056684, 0.051002070265275305, 0.10033434965157292, 0.08866609562703809, 0.04706051710560124, 0.04624467321454656, 0.02488989912809945, 0.08473039311898925, 0.13450241464245608, 0.06227779683894158, 0.04114296764716252, 0.06097095425203603, 0.16138564352732188, 0.13000858591951922, 0.07893198811508031, 0.0174537309540991, 0.12877959795074775, 0.11799894350244756, 0.13761276966043087, 0.09622236591130363, 0.014771259930609622, 0.03499983678086293, 0.06552470068264275, 0.11625374266086884, 0.014718525427675062, 0.06589735514005671, 0.09659628183631323, 0.03630771618898802, 0.01653300494140565, 0.057478381622757375, 0.09814199343595481, 0.1288508104020973, 0.0784679513847759, 0.12971599231150696, 0.18068582617873133, 0.12006281658150263, 0.12055394192317093, 0.16162178396275825, 0.1417439705651028, 0.142403731992361, 0.16360083491739905, 0.12374006844327716, 0.18556855778868017, 0.10519016360204618, 0.30892879195514955, 0.18759722696226178, 0.14812674296885198, 0.12885318879950797, 0.17063926926199985, 0.13132646865452355, 0.04148654003444211, 0.12455433497663353, 0.08551463873403654, 0.2496114248866563, 0.17967532476246378, 0.22171085529184192, 0.18242901037161285, 0.18382535102621345, 0.1651247055687966, 0.1569351999845064, 0.1691121456912243, 0.17092693805998516, 0.13271511609203906, 0.2769897988393457, 0.25835559649970213, 0.0981784686292238, 0.22244308501417126, 0.20416282604579084, 0.2669034524510536, 0.18760038245120017, 0.16944551993568518, 0.19202418108286468, 0.18419500308628178, 0.17668529396725494, 0.17906726991330205, 0.2015271530566169, 0.1934090343112588, 0.18571929818754893, 0.35004063005156166, 0.12843523769671772, 0.19186877078864337, 0.14364383180112045, 0.08571664345906294, 0.1396838930262815, 0.10185375804934753, 0.16573335306227127, 0.168649666244033, 0.15100676238152888, 0.2243262837688848, 0.27725268801413583, 0.1983552332438529, 0.16023255938116024, 0.16287223661595726, 0.12490948094260519, 0.14819278936414468, 0.11056174818140603, 0.19465918448727043, 0.1765506539535321, 0.12824188201881664, 0.22236798169438546, 0.18445460717284431, 0.18701739759714237, 0.1488032748572058, 0.161477820764309, 0.19447967139338726, 0.13606253737496324, 0.21945725698002466, 0.20114097958362243, 0.16274445754557562, 0.1850807100263151, 0.1566748573130659, 0.09828283261515203, 0.1721931463987686, 0.17426379118183574, 0.05485872250991026, 0.15924108409567198, 0.12099117424406731, 0.10367331425262863, 0.11642089768755905, 0.18955520006231674, 0.171483176531961, 0.0015331356286049762, 0.11608806343296257, 0.058074053935762066, 0.25338508219006406, 0.113617139812531, 0.12638759531430152, 0.02774761426921538, 0.07169605230664543, 0.07445287652849375, 0.04681799946476232, 0.14072566242453494, 0.10240630659145168, 0.023777411885068808, 0.24884974379242555, 0.0073702797557595266, 0.030521189987765564, 0.012757487248425865, 0.03516734498378387, 0.03797979584328547, 0.010173892861520444, 0.04290523878161734, 0.0958838096206042, 0.01667560149252222, 0.07002079506087884, 0.07231854420935557, 0.05434426902003412, 0.005802650698593769, 0.11975453878848655, 0.08091866755475204, 0.10306456095974116, 0.08456554995495855, 0.10695633874838802, 0.23011834843286635, 0.12958816551703214], "yaxis": "y"}], "layout": {"legend": {"title": {"text": "variable"}, "tracegroupgap": 0}, "margin": {"t": 60}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "index"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "value"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["df['relative_range'].plot()"]}], "metadata": {"interpreter": {"hash": "42c9c6e43b9ee0fc22687a48068a5a65b535cb622a7606f9009c39c135991202"}, "kernelspec": {"display_name": "Python 3.8.5 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}