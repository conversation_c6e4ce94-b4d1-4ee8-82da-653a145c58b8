import os
import pandas as pd
from urllib import parse
import configparser
import sqlalchemy
from sqlalchemy.ext.automap import automap_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import MetaData, create_engine
import pymysql
import cx_Oracle


def list_stations_by_item_sqlalchemy(methodname='地电阻率', database='shandong-official'):

    # connect to database
    os.environ['NLS_LANG'] = 'AMERICAN_AMERICA.ZHS16GBK'
    config = configparser.ConfigParser()
    config.read('default.conf')
    url = sqlalchemy.engine.URL.create(config[database]['DRIVER'],
                                    username=config[database]['USERNAME'],
                                    password=config[database]['PASSWORD'],
                                    host=config[database]['HOST'],
                                    port=config[database]['PORT'],
                                    database=config[database]['TNSNAME'])
    user = config[database]['USERNAME']
    password = parse.quote_plus(config[database]['PASSWORD'])
    host = config[database]['HOST']
    db = config[database]['TNSNAME']

    # url = f'mysql+pymysql://{user}:{password}@{host}:3308/{db}?charset=utf8'
    engine = create_engine(url)
    metadata = MetaData(bind=engine)
    metadata.reflect(schema='QZDATA', only=['QZ_DICT_STATIONS', 'QZ_DICT_STATIONITEMS', 'QZ_DICT_ITEMS'])
    Base = automap_base(metadata=metadata)
    Base.prepare()
    qz_dict_stations = Base.classes.QZ_DICT_STATIONS
    qz_dict_stationitems = Base.classes.QZ_DICT_STATIONITEMS
    qz_dict_items = Base.classes.QZ_DICT_ITEMS
    Session = sessionmaker(bind=engine)
    session = Session()
    sql = session.query(qz_dict_stations.STATIONID, qz_dict_stations.STATIONNAME).\
                    distinct(qz_dict_stations.STATIONID).\
                        join(qz_dict_stationitems, qz_dict_stations.STATIONID==qz_dict_stationitems.STATIONID).\
                            join(qz_dict_items, qz_dict_stationitems.ITEMID==qz_dict_items.ITEMID).\
                                filter(qz_dict_items.ITEMNAME.like(f'%{methodname}%')).statement
    stations = pd.read_sql(sql=sql, con=engine)

    return stations

cx_Oracle.init_oracle_client(lib_dir='/Users/<USER>/Work.localized/Python/instantclient')
sta = list_stations_by_item_sqlalchemy()
print(sta)
