class annual_amplitude(Resource):

    def post(self):
        '''计算视电阻率年变化的年变化幅度
        ---
        tags:
          - 年变化幅度
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: upfile
            in: formData
            type: file
            required: true
            description: 数据需包含趋势、年变化时间序列（utf-8）

        responses:
          200:
            description: 计算视电阻率年变化的年变化幅度，利用希尔伯特变换方法
        '''
        uploaded_file = request.files['upfile']
        data = pd.read_json(uploaded_file, orient='split')
        data = data.tz_localize(None)
        df = self.res_hilbert(data)

        return jsonify(df.to_dict(orient='split'))

    @staticmethod
    def forward_std(df):
        df_r = pd.DataFrame()
        df_r['relative_range'] = df['relative_range']
        df_r['relative_range'] = df_r['relative_range'].pow(2).cumsum()
        df_r['k'] = df_r.reset_index().index + 1
        df_r['new_threshold'] = df_r['relative_range'] / df_r['k']
        df_r['new_threshold'] = df_r['new_threshold'].pow(1 / 2)

        return df_r['new_threshold'].mean()

    def res_hilbert(self, df):
        from scipy.signal import hilbert
        df['hilbert'] = np.abs(hilbert(df['fourier']))
        df['annual_1'] = ((df['hilbert']) * 100 / df['trend']).abs() / 2
        df['annual_1'][df.index[0]:df.index[180]] = df['annual_1'][
            df.index[181]]
        df['annual_1'][df.index[-180]:df.index[-1]] = df['annual_1'][
            df.index[-181]]
        df['annual_2'] = -df['annual_1']

        df_r = pd.DataFrame()
        df_r['relative_range'] = df['relative_range']
        df_r['relative_range'] = df_r['relative_range'].pow(2).cumsum()
        df_r['k'] = df_r.reset_index().index + 1
        df_r['new_threshold'] = df_r['relative_range'] / df_r['k']
        df_r['new_threshold'] = df_r['new_threshold'].pow(1 / 2)

        df['std_1'] = df_r['new_threshold'].mean() * 2.5
        df['std_2'] = -df_r['new_threshold'].mean() * 2.5

        return df
