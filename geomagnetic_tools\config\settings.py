"""
应用程序配置设置

定义不同环境下的配置参数
"""

import os
from pathlib import Path

# 项目根目录
BASE_DIR = Path(__file__).parent.parent.parent


class Config:
    """基础配置类"""
    
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    JSON_AS_ASCII = False
    
    # Swagger配置
    SWAGGER_TITLE = '电磁学科时间序列处理接口'
    SWAGGER_DESCRIPTION = '电磁学科时间序列处理方法，差分、地磁加卸载响应比、地电场优势方位角等'
    SWAGGER_VERSION = '1.0.0'
    SWAGGER_HOST = '0.0.0.0:8888'
    
    # 文件上传配置
    UPLOAD_FOLDER = BASE_DIR / 'data' / 'uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 数据目录配置
    DATA_DIR = BASE_DIR / 'data'
    STEPS_DIR = DATA_DIR / 'steps'
    FIGURES_DIR = BASE_DIR / 'figures'
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = BASE_DIR / 'logs' / 'app.log'
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 创建必要的目录
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(Config.FIGURES_DIR, exist_ok=True)
        os.makedirs(Config.LOG_FILE.parent, exist_ok=True)


class DevelopmentConfig(Config):
    """开发环境配置"""
    
    DEBUG = True
    TESTING = False
    
    # 开发环境数据库配置
    DATABASE_URL = os.environ.get('DEV_DATABASE_URL') or 'sqlite:///dev.db'
    
    # 开发环境日志级别
    LOG_LEVEL = 'DEBUG'


class ProductionConfig(Config):
    """生产环境配置"""
    
    DEBUG = False
    TESTING = False
    
    # 生产环境数据库配置
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'sqlite:///prod.db'
    
    # 生产环境安全配置
    SECRET_KEY = os.environ.get('SECRET_KEY')
    if not SECRET_KEY:
        raise ValueError("生产环境必须设置SECRET_KEY环境变量")
    
    # 生产环境日志配置
    LOG_LEVEL = 'WARNING'
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # 生产环境特定初始化
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug:
            file_handler = RotatingFileHandler(
                cls.LOG_FILE, 
                maxBytes=10240000, 
                backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            app.logger.setLevel(logging.INFO)
            app.logger.info('Geomagnetic Tools startup')


class TestingConfig(Config):
    """测试环境配置"""
    
    DEBUG = True
    TESTING = True
    
    # 测试数据库配置
    DATABASE_URL = 'sqlite:///:memory:'
    
    # 测试环境禁用CSRF保护
    WTF_CSRF_ENABLED = False


# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
