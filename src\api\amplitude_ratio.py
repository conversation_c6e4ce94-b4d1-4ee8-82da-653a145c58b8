from flask_restful import reqparse, Api, Resource


class amplitude_ratio(Resource):

    def get(self):
        '''地磁逐日比
        ---
        tags:
          - 电磁
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: database
            in: query
            type: string
            default: 'shandong-official'
            description: 数据库名称
          - name: stationname
            in: query
            type: string
            default: '泰安'
            description: 台站名称
          - name: startdate
            in: query
            type: string
            default: '20210401'
            description: 起始时间
          - name: enddate
            in: query
            type: string
            default: '20210410'
            description: 结束时间
          - name: returntype
            in: query
            type: boolean
            default: false
            description: false:JSON数据表/true:JSON图片

        responses:
          200:
            description: 用于计算地磁逐日比
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('database',
                            type=str,
                            required=False,
                            location='args',
                            help='database')
        parser.add_argument('startdate',
                            type=str,
                            required=False,
                            location='args',
                            help='startdate')
        parser.add_argument('enddate',
                            type=str,
                            required=False,
                            location='args',
                            help='enddate')
        parser.add_argument('stationname',
                            type=str,
                            required=False,
                            location='args',
                            help='stationname')
        parser.add_argument('returntype',
                            type=str,
                            required=False,
                            location='args',
                            help='return type')
        args = parser.parse_args()
        print(args)
        database = args['database']
        startdate = args['startdate']
        enddate = args['enddate']
        stationname = args['stationname']
        returntype = args['returntype']
        print(returntype)
        if returntype == 'true':
            df = self.magnetic_daily_ratio_vertical(startdate,
                                                    enddate,
                                                    stationname=stationname,
                                                    figure_or_not=True,
                                                    database=database)
            return jsonify({'figure': df})
        else:
            df = self.magnetic_daily_ratio_vertical(startdate,
                                                    enddate,
                                                    stationname=stationname,
                                                    figure_or_not=False,
                                                    database=database)
            return jsonify(df.to_dict(orient='dict'))

    @staticmethod
    def harmonic_analysis(df, order):
        df.fillna(method='bfill', inplace=True)
        n = df.shape[0]
        df['IND'] = range(1, n + 1)
        df['FITTING'] = df['OBSVALUE'].mean()
        for i in range(1, order + 1):
            ak = sum(
                df['OBSVALUE'] * cos(2.0 * pi * df['IND'] / n * i)) / n * 2.0
            bk = sum(
                df['OBSVALUE'] * sin(2.0 * pi * df['IND'] / n * i)) / n * 2.0
            df['FITTING'] = df['FITTING'] \
                            + ak * cos(2.0 * pi * df['IND'] * i / n) \
                            + bk * sin(2.0 * pi * df['IND'] * i / n)
        return df

    @staticmethod
    def lowpass_filter(df):
        df.fillna(method='bfill', inplace=True)
        b, a = signal.butter(N=8,
                             Wn=1 / 2400,
                             btype='lowpass',
                             fs=1 / 60,
                             analog=False,
                             output='ba')
        f_pad = signal.filtfilt(b=b, a=a, x=df['OBSVALUE'].values, padlen=50)
        df['FITTING'] = f_pad
        return df

    @staticmethod
    def harmonic_analysis_for_mdrv(df, order=48):
        for column in df.columns.values:
            df_ha = pd.DataFrame()
            df_ha['OBSVALUE'] = df[column]
            df_ha = amplitude_ratio.lowpass_filter(df_ha)
            df[column] = df_ha['FITTING']
        return df

    def magnetic_daily_ratio_vertical(
        self,
        startdate,
        enddate,
        stationname='全部',
        figure_or_not=True,
        database='BAIJIATUAN',
    ):

        conn = tsf.conn_to_MySQL(database)
        df = tsf.fetching_data(conn,
                               startdate,
                               enddate,
                               '地磁',
                               stationname,
                               '分钟值',
                               '预处理库',
                               gzip_flag=False,
                               itemname='变化记录垂直分量Z')
        stations, points, items = tsf.timeseries_info(conn)
        df = df.tz_localize('UTC')
        df = df.tz_convert('Asia/Shanghai')
        # groupby STATIONID
        df = df.pivot_table(values='OBSVALUE',
                            index='STARTDATE',
                            columns='STATIONID')
        # ratio
        days = pd.date_range(df.index.min(), df.index.max())
        df_amplitude = pd.DataFrame()
        for i1 in days:
            day = i1.strftime('%Y-%m-%d')
            # Fourier filter
            df_amplitude_tmp = self.harmonic_analysis_for_mdrv(df.loc[day],
                                                               order=48)
            amplitude = df_amplitude_tmp.max() - df_amplitude_tmp.min()
            df_amplitude = df_amplitude.append(amplitude, ignore_index=True)
        df_amplitude.index = days.strftime('%Y-%m-%d')

        amplitude_1 = df_amplitude[+1:]
        amplitude_2 = df_amplitude[:-1]
        amplitude_2.index = amplitude_1.index
        df_ratio = amplitude_2 / amplitude_1

        # add station name
        sta = df_ratio.columns.tolist()
        if figure_or_not:
            station_name = list()
            for i in range(df_ratio.shape[1]):
                station_name.append(
                    stations[stations['STATIONID'] == sta[i][0:5]].iloc[0, 1] +
                    sta[i])
            # df_ratio.columns = station_name
            pd.options.plotting.backend = 'plotly'
            trace = list()
            for column in df.columns.values:
                trace.append(go.Scatter(x=df_ratio.index, y=df_ratio[column]))
            fig = make_subplots(
                rows=len(trace),
                cols=1,
                subplot_titles=station_name,
                x_title="Date",
                y_title="",
            )
            for i in range(0, len(trace)):
                fig.append_trace(trace[i], i + 1, 1)
            fig.update_layout(height=200 * len(trace),
                              width=1200,
                              title_text='地磁逐日比')
            df_ratio = fig.to_json()

        return df_ratio
