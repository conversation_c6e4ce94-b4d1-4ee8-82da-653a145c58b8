class geomagnetic_harmonic_ratio(Resource):

    def get(self):
        '''地磁谐波振幅比
        ---
        tags:
          - 电磁
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: stationname
            in: query
            type: string
            default: '泰安'
            description: 台站名称
          - name: database
            in: query
            type: string
            default: 'shandong-official'
            description: 数据库名称
          - name: startdate
            in: query
            type: string
            default: '20210101'
            description: 起始时间
          - name: enddate
            in: query
            type: string
            default: '20210131'
            description: 结束时间
          - name: returntype
            in: query
            type: boolean
            default: false
            description:  false:JSON数据表/true:JSON图片

        responses:
          200:
            description: 用于计算地磁谐波振幅比
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('startdate',
                            type=str,
                            required=False,
                            location='args',
                            help='startdate')
        parser.add_argument('enddate',
                            type=str,
                            required=False,
                            location='args',
                            help='enddate')
        parser.add_argument('stationname',
                            type=str,
                            required=False,
                            location='args',
                            help='stationname')
        parser.add_argument('database',
                            type=str,
                            required=False,
                            location='args',
                            help='database')
        parser.add_argument('returntype',
                            type=str,
                            required=False,
                            location='args',
                            help='return type')
        args = parser.parse_args()
        stationname = args['stationname']
        database = args['database']
        startdate = args['startdate']
        enddate = args['enddate']
        returntype = args['returntype']
        if returntype == 'true':
            df = self.harmonic_ratio(startdate,
                                     enddate,
                                     stationname=stationname,
                                     figure_or_not=True,
                                     database=database)
            return jsonify({'figure': df})
        else:
            df = self.harmonic_ratio(startdate,
                                     enddate,
                                     stationname=stationname,
                                     figure_or_not=False,
                                     database=database)
            return jsonify(df.to_dict(orient='dict'))

    def harmonic_ratio(self,
                       startdate,
                       enddate,
                       stationname='泰安',
                       figure_or_not=True,
                       database='BAIJIATUAN'):

        conn = tsf.conn_to_MySQL(database)
        df = tsf.fetching_data(conn,
                               startdate,
                               enddate,
                               '地磁',
                               stationname,
                               '分钟值',
                               '预处理库',
                               gzip_flag=False)
        stations, points, items = tsf.timeseries_info(conn)
        # TimeZone
        df = df.tz_localize('UTC')
        df = df.tz_convert('Asia/Shanghai')
        df_harmonic = self.calculate_harmonic_ratio(df)
        if figure_or_not:
            trace = px.scatter(df_harmonic.rolling(30).mean(),
                               labels={
                                   'index': 'Date',
                                   'value': 'Harmonic ratio',
                                   'variable': '测向'
                               },
                               title='地磁谐波振幅比')
            trace.show()
            df_harmonic = trace.to_json()
        return df_harmonic

    def calculate_harmonic_ratio(self, df):
        df_z = df[(df['ITEMID'] == '3123') & (df['STATIONID'] == '370011')]
        df_h = df[(df['ITEMID'] == '3124') & (df['STATIONID'] == '370011')]
        days = pd.date_range(df.index.min(), df.index.max(), freq='1D')
        df_harmonic = pd.DataFrame()
        for i in days:
            day = i.strftime('%Y-%m-%d')
            print(day)
            # df_oneday_z = df_z.loc[day: (i + dt.timedelta(days=5)).strftime('%Y-%m-%d')]
            # df_oneday_h = df_h.loc[day: (i + dt.timedelta(days=5)).strftime('%Y-%m-%d')]
            df_oneday_z = df_z.loc[day]
            df_oneday_h = df_h.loc[day]
            df_harmonic_oneday = self.calculate_harmonic_oneday(
                df_oneday_z, df_oneday_h)
            df_harmonic = df_harmonic.append(df_harmonic_oneday,
                                             ignore_index=True)
        df_harmonic.index = days.strftime('%Y-%m-%d')
        return df_harmonic

    def calculate_harmonic_oneday(self, df_1, df_2):
        import pydsm
        df_harmonic_oneday = dict()
        df_1.fillna(method='bfill', inplace=True, limit=1440)
        df_2.fillna(method='bfill', inplace=True, limit=1440)
        try:
            df_1['detrend'] = signal.detrend(df_1['OBSVALUE'])
            df_2['detrend'] = signal.detrend(df_2['OBSVALUE'])
            F_1 = pydsm.ft.dtft(df_1['detrend'], fs=1 / 60)
            F_2 = pydsm.ft.dtft(df_2['detrend'], fs=1 / 60)
            freq = 1 / 600
            # freq = 5.78125e-05
            df_harmonic_oneday['harmonic'] = abs(F_1(freq)) / abs(F_2(freq))
        except:
            df_harmonic_oneday['harmonic'] = np.nan

        return df_harmonic_oneday

    @staticmethod
    def fft_ck(df, order):
        df.fillna(method='bfill', inplace=True)
        df_fft = fft(df['OBSVALUE'])
        tmp = 2.0 * abs(df_fft) / 1440
        ck = sum(tmp[1:order + 1])
        return ck
