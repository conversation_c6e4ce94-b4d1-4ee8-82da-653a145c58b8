class wavelets_trend(Resource):

    def post(self):
        '''利用小波分解方法提取数据变化趋势
        ---
        tags:
          - 小波分解
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: upfile
            in: formData
            type: file
            required: true
            description: 利用傅立叶去年变方法提取的去年变后的时间序列（utf-8）

        responses:
          200:
            description: 利用小波分解方法提取数据变化趋势
        '''
        uploaded_file = request.files['upfile']
        data = pd.read_json(uploaded_file, orient='split')
        data = data.tz_localize(None)
        df = self.wavelets_dec(data)

        return jsonify(df.to_dict(orient='split'))

    def wavelets_dec(self, df):
        import pywt

        wavelet_type = 'db10'
        wavelet_level = 10
        w = pywt.Wavelet(wavelet_type)
        df['residual'].fillna(inplace=True, method='ffill')
        signal = df['residual']
        coeffs = pywt.wavedec(signal,
                              wavelet=w,
                              mode='reflect',
                              level=wavelet_level)
        rec_sig_a = pywt.upcoef('a', coeffs[0], w, level=10, take=len(signal))
        df['trend'] = rec_sig_a
        df_trend = pd.DataFrame()
        df_trend['trend'] = df['trend']
        df_trend['residual'] = df['residual']
        fig = df_trend.plot()
        return df


class relative_range(Resource):

    def post(self):
        '''计算去年变时间序列的相对变化幅度
        ---
        tags:
          - 相对变化幅度
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: upfile
            in: formData
            type: file
            required: true
            description: 数据需包含趋势变化、残差时间序列（utf-8）

        responses:
          200:
            description: 计算去年变时间序列的相对变化幅度
        '''
        uploaded_file = request.files['upfile']
        data = pd.read_json(uploaded_file, orient='split')
        data = data.tz_localize(None)
        df = self.calculate_relative_range(data)

        return jsonify(df.to_dict(orient='split'))

    def calculate_relative_range(self, df):
        import pywt
        pd.options.plotting.backend = 'plotly'
        wavelet_type = 'db10'
        wavelet_level = 5
        w = pywt.Wavelet(wavelet_type)
        signal = df['residual']
        coeffs = pywt.wavedec(signal,
                              wavelet=w,
                              mode='reflect',
                              level=wavelet_level)
        rec_sig_0 = pywt.upcoef('a', coeffs[0], w, level=5, take=len(signal))
        rec_sig = rec_sig_0
        df['new_residual'] = rec_sig
        df['flag'] = False
        df['flag_0'] = df['new_residual'] - df['trend']
        df['flag_1'] = df['flag_0'].shift(1)
        df['flag_2'] = df['flag_1'] * df['flag_0']
        df.loc[df['flag_2'] < 0, 'flag'] = True
        df.drop(['flag_0', 'flag_1', 'flag_2'], axis=1, inplace=True)
        df['start'] = df['residual']
        df.iloc[0, 5] = True
        df.loc[df['flag'] == False, 'start'] = np.nan
        df.fillna(method='ffill', inplace=True)
        df['relative_range'] = (df['residual'] - df['start']) / df['start']
        df['relative_range'] = df['relative_range'] * 100
        fig = df['relative_range'].plot()
        # fig.show()

        return df
