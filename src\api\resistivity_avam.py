class resistivity_avam(Resource):

    def get(self):
        '''自适应变化幅度方法提取地电阻率异常
        ---
        tags:
          - 数据库
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: database
            in: query
            type: string
            default: 'shandong-official'
            description: 数据库名称
          - name: stationname
            in: query
            type: string
            default: '马陵山'
            description: 台站名称
          - name: startdate
            in: query
            type: string
            default: '20150101'
            description: 起始时间
          - name: enddate
            in: query
            type: string
            default: '20211231'
            description: 结束时间
          - name: methodname
            in: query
            type: string
            default: '地电阻率'
            description: 测向名称，模糊查询
          - name: sampling
            in: query
            type: string
            default: '小时值'
            description: 采样率，模糊查询
          - name: basetype
            in: query
            type: string
            default: '预处理库'
            description: 数据类型：预处理库/原始库/产品库
          - name: itemname
            in: query
            type: string
            default: '直流单装置地电阻率观测东西向'
            description: 测向分量：精确匹配
          - name: pointid
            in: query
            type: string
            default: ''
            description: 测点号，台站只有一套仪器不需要填
          - name: gaps
            in: query
            type: string
            default: ''
            description: 缺数区间
          - name: returntype
            in: query
            type: boolean
            default: false
            description:  false:JSON数据表/true:JSON图片

        responses:
          200:
            description: 自适应变化幅度方法提取地电阻率异常
        '''
        parser = reqparse.RequestParser(bundle_errors=True)
        parser.add_argument('database',
                            type=str,
                            required=False,
                            location='args',
                            help='database')
        parser.add_argument('startdate',
                            type=str,
                            required=False,
                            location='args',
                            help='startdate')
        parser.add_argument('enddate',
                            type=str,
                            required=False,
                            location='args',
                            help='enddate')
        parser.add_argument('stationname',
                            type=str,
                            required=False,
                            location='args',
                            help='stationname')
        parser.add_argument('methodname',
                            type=str,
                            required=False,
                            location='args',
                            help='methodname')
        parser.add_argument('sampling',
                            type=str,
                            required=False,
                            location='args',
                            help='sampling')
        parser.add_argument('basetype',
                            type=str,
                            required=False,
                            location='args',
                            help='basetype')
        parser.add_argument('itemname',
                            type=str,
                            required=False,
                            location='args',
                            help='itemname')
        parser.add_argument('pointid',
                            type=str,
                            required=False,
                            location='args',
                            help=u'台站只有一套仪器不需要填')
        parser.add_argument('gaps',
                            type=str,
                            required=False,
                            location='args',
                            help='gaps')
        parser.add_argument('returntype',
                            type=str,
                            required=False,
                            location='args',
                            help='return type')
        args = parser.parse_args()
        database = args['database']
        stationname = args['stationname']
        startdate = args['startdate']
        enddate = args['enddate']
        methodname = args['methodname']
        sampling = args['sampling']
        basetype = args['basetype']
        itemname = args['itemname']
        pointid = args['pointid']

        print(f'point id:{pointid}')
        if not pointid:
            pointid = ''

        conn = tsf.conn_to_MySQL(database)

        df = tsf.fetching_data(conn,
                               startdate,
                               enddate,
                               methodname,
                               stationname,
                               sampling,
                               basetype,
                               gzip_flag=False,
                               itemname=itemname,
                               pointid=pointid)

        # df = df.groupby(['ITEMID', 'STATIONID']).resample('1D').median(numeric_only=True)
        # df.reset_index(inplace=True)
        # df.set_index('STARTDATE', inplace=True)

        rs = remove_steps()
        df = rs.remove_steps_by_logs(df)
        fs = fourier_smoothing()
        df = fs.fourier_removing(df)
        wt = wavelets_trend()
        df = wt.wavelets_dec(df)
        rr = relative_range()
        df = rr.calculate_relative_range(df)
        aa = annual_amplitude()
        df = aa.res_hilbert(df)
        return jsonify(df.to_dict(orient='split'))

    def post(self):
        '''自适应变化幅度方法提取地电阻率异常
        ---
        tags:
          - 数据库
        consumes:
          - multipart/form-data
        produces:
          - application/json
        parameters:
          - name: upfile
            in: formData
            type: file
            required: true
            description: 利用自适应变化幅度方法提取地电阻率异常

        responses:
          200:
            description: 自适应变化幅度方法提取地电阻率异常
        '''

        uploaded_file = request.files['upfile']
        df = pd.read_json(uploaded_file)

        fs = fourier_smoothing()
        df = fs.fourier_removing(df)
        wt = wavelets_trend()
        df = wt.wavelets_dec(df)
        rr = relative_range()
        df = rr.calculate_relative_range(df)
        aa = annual_amplitude()
        df = aa.res_hilbert(df)
        return jsonify(df.to_dict(orient='split'))
